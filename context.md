# Projet: Application de Playlist Collaborative en Temps Réel pour Événements

**Objectif Principal:**

Concevoir et développer une application web permettant à plusieurs participants de collaborer en temps réel sur une playlist musicale lors d'un événement convivial (soirée, réunion, etc.). L'objectif est de simplifier l'accès, de proposer une interface intuitive et d'intégrer des mécanismes d'interaction amusants (votes emoji, gamification).

**Rôles Utilisateurs:**

1.  **Hôte:**
    *   Crée et gère la session.
    *   Dispose du contrôle total sur la lecture audio (lecture, pause, suivant, précédent, volume).
    *   Accès à un tableau de bord de gestion.
    *   Peut modérer la playlist.

2.  **Participant:**
    *   Rejoint une session existante via un code ou un QR code.
    *   Peut soumettre des titres à ajouter à la playlist.
    *   Peut voter (via emojis) sur les titres proposés.
    *   Visualise la playlist en temps réel.
    *   Suit la lecture en cours.
    *   *Ne peut pas* contrôler la lecture ou modifier directement la playlist (sauf par le vote/soumission).

**Fonctionnalités Clés Requises:**

*   **Gestion des Sessions:**
    *   Création de session unique par l'Hôte.
    *   Génération d'un code de session ou QR code pour inviter les Participants.
    *   Jointure rapide à une session via code/QR code (authentification minimale).
*   **Soumission de Titres:**
    *   Interface intuitive pour rechercher et ajouter des musiques.
    *   Gestion des doublons: Ne pas ajouter deux fois le même titre; si déjà présent, augmenter sa "priorité" ou "visibilité" (à définir, par ex. le déplacer vers le haut).
*   **Vote en Temps Réel:**
    *   Les Participants peuvent réagir aux titres avec des emojis (positifs/négatifs).
    *   La position des titres dans la playlist s'ajuste dynamiquement en fonction du total des réactions/votes (système de pondération à définir).
*   **Affichage en Temps Réel:**
    *   Synchronisation instantanée de la playlist pour tous les Participants et l'Hôte.
    *   Affichage clair du titre en cours de lecture pour tous.
*   **Contrôle Hôte:**
    *   Interface dédiée à l'Hôte pour les contrôles de lecture (Play, Pause, Suivant, Précédent, Volume).
*   **Notifications et Engagement:**
    *   Alertes pour inciter à ajouter des titres (par ex. si la file d'attente est courte ou l'activité baisse).
    *   Notifications en temps réel des changements majeurs dans la playlist (nouveau titre ajouté, ordre changé).
*   **Gamification:**
    *   Système de points attribués aux Participants en fonction du succès (votes positifs) de leurs propositions.
    *   Bonus de points pour les titres les plus plébiscités.
    *   Mise en place de badges ou d'un classement (leaderboard) pour encourager la contribution de qualité.
*   **Tableau de Bord Hôte (Avancé):**
    *   Outils de modération (supprimer des titres inappropriés).
    *   Options de gestion de session (verrouiller la soumission, ajuster l'impact des votes).
*   **Gestion et Exportation de la Playlist:**
    *   La playlist est l'état central, dynamiquement ordonnée.
    *   Possibilité d'exporter la playlist finale, idéalement vers Spotify.
*   **(Optionnel) Fonctionnalité Sociale:**
    *   Un chat simple en temps réel au sein de la session.

**Points Clés de Mise en Œuvre Techniques:**

*   **Intégration Spotify:**
    *   Utiliser l'API Spotify pour la recherche de titres.
    *   Utiliser l'API Spotify pour le contrôle de la lecture (côté Hôte - nécessite un compte Spotify Premium pour l'Hôte).
    *   Utiliser l'API Spotify pour la création et l'exportation de la playlist finale.
*   **Architecture Temps Réel:**
    *   Nécessite l'utilisation de WebSockets ou une technologie similaire (Socket.IO, etc.) pour la synchronisation instantanée des playlists, votes, et statut de lecture.
*   **Système d'Accès Simplifié:**
    *   Mécanisme léger d'authentification/session pour les Participants (pas forcément un compte utilisateur complet, juste un identifiant de session et éventuellement un pseudo).
*   **Logique de Gamification:**
    *   Implémenter le suivi des points et des badges basés sur les interactions.

**Contexte VS Code / Claude:**

Je travaille sur ce projet dans VS Code. Je voudrais que tu m'aides à structurer le code et à implémenter les différentes parties.

**Tâche pour Claude:**

1.  **Proposer une Stack Technique (Frontend/Backend/Base de données)** adaptée pour une application web collaborative en temps réel avec intégration d'API externes (Spotify). Justifiez brièvement vos choix.
2.  **Ébaucher le Modèle de Données** principal nécessaire pour représenter les `Sessions`, les `Participants`, les `Titres proposés`, et les `Votes`. Utilisez un format lisible (par ex. liste ou simple schéma textuel).
3.  **Décrire les Principaux Endpoints API** qui seraient nécessaires pour gérer les sessions, la soumission de titres, les votes, et le contrôle hôte.

Commence par ces points pour poser les bases du projet.