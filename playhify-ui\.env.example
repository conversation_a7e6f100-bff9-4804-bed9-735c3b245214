# Configuration PlayHify - Exemple
# Copiez ce fichier vers .env.local et remplissez les valeurs

# Spotify Configuration
# Obtenez ces valeurs depuis https://developer.spotify.com/dashboard
NEXT_PUBLIC_SPOTIFY_CLIENT_ID=your_spotify_client_id_here
NEXT_PUBLIC_SPOTIFY_REDIRECT_URI=http://localhost:3000/auth/spotify/callback

# Supabase Configuration
# Obtenez ces valeurs depuis votre projet Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# API Configuration
# URL de votre backend API
NEXT_PUBLIC_API_URL=http://localhost:3001/api

# WebSocket Configuration (optionnel)
NEXT_PUBLIC_WS_URL=ws://localhost:3001

# Environment
NODE_ENV=development

# Analytics (optionnel)
NEXT_PUBLIC_GA_ID=your_google_analytics_id
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id

# Sentry (optionnel pour le monitoring d'erreurs)
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn
