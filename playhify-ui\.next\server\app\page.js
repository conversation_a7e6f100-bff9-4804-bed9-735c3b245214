/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMAISON%5CDocuments%5CCline%5Cplayhifyy%5Cplayhify-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMAISON%5CDocuments%5CCline%5Cplayhifyy%5Cplayhify-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMAISON%5CDocuments%5CCline%5Cplayhifyy%5Cplayhify-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMAISON%5CDocuments%5CCline%5Cplayhifyy%5Cplayhify-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMAISON%5CDocuments%5CCline%5Cplayhifyy%5Cplayhify-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMAISON%5CDocuments%5CCline%5Cplayhifyy%5Cplayhify-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Ccomponents%5C%5CSimpleToast.tsx%22%2C%22ids%22%3A%5B%22SimpleToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Ccontext%5C%5CAppContext.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Ccomponents%5C%5CSimpleToast.tsx%22%2C%22ids%22%3A%5B%22SimpleToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Ccontext%5C%5CAppContext.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SimpleToast.tsx */ \"(ssr)/./src/components/SimpleToast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/AppContext.tsx */ \"(ssr)/./src/context/AppContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Ccomponents%5C%5CSimpleToast.tsx%22%2C%22ids%22%3A%5B%22SimpleToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Ccontext%5C%5CAppContext.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q01BSVNPTiU1QyU1Q0RvY3VtZW50cyU1QyU1Q0NsaW5lJTVDJTVDcGxheWhpZnl5JTVDJTVDcGxheWhpZnktdWklNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW9IIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGxheWhpZnktdWkvP2U2NmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxNQUlTT05cXFxcRG9jdW1lbnRzXFxcXENsaW5lXFxcXHBsYXloaWZ5eVxcXFxwbGF5aGlmeS11aVxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMAISON%5C%5CDocuments%5C%5CCline%5C%5Cplayhifyy%5C%5Cplayhify-ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Music,Plus,QrCode,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/music.js\");\n/* harmony import */ var _barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Music,Plus,QrCode,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Music,Plus,QrCode,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Music,Plus,QrCode,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_SimpleToast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SimpleToast */ \"(ssr)/./src/components/SimpleToast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [sessionCode, setSessionCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const toast = (0,_components_SimpleToast__WEBPACK_IMPORTED_MODULE_3__.useSimpleToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-12 h-12 text-white mr-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl font-bold text-white\",\n                                    children: \"PlayHify\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                            children: \"Cr\\xe9ez des playlists collaboratives en temps r\\xe9el pour vos \\xe9v\\xe9nements. Laissez vos invit\\xe9s ajouter leurs musiques pr\\xe9f\\xe9r\\xe9es et voter pour leurs titres favoris !\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto grid md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-8 h-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-4\",\n                                        children: \"Cr\\xe9er une Session\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6\",\n                                        children: \"D\\xe9marrez une nouvelle session de playlist collaborative. Vous aurez le contr\\xf4le total sur la lecture et pourrez mod\\xe9rer les ajouts.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/create-session\",\n                                        className: \"inline-flex items-center px-6 py-3 bg-green-500 text-white rounded-lg font-semibold hover:bg-green-600 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cr\\xe9er une Session\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-8 h-8 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white mb-4\",\n                                        children: \"Rejoindre une Session\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6\",\n                                        children: \"Participez \\xe0 une session existante avec un code d'invitation. Ajoutez vos musiques et votez pour vos titres pr\\xe9f\\xe9r\\xe9s !\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Code de session (ex: ABC123)\",\n                                                    value: sessionCode,\n                                                    onChange: (e)=>setSessionCode(e.target.value.toUpperCase()),\n                                                    className: \"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                    maxLength: 6\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this),\n                                            sessionCode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: `/session/${sessionCode}`,\n                                                className: \"inline-flex items-center px-6 py-3 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Rejoindre\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toast.warning(\"Code requis\", \"Veuillez entrer un code de session pour continuer\"),\n                                                className: \"inline-flex items-center px-6 py-3 bg-gray-500 text-gray-300 rounded-lg font-semibold cursor-not-allowed\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Rejoindre\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-20 max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-3xl font-bold text-white text-center mb-12\",\n                            children: \"Fonctionnalit\\xe9s\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-semibold text-white mb-2\",\n                                            children: \"Int\\xe9gration Spotify\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Recherchez et ajoutez des musiques directement depuis Spotify\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-xl\",\n                                                children: \"\\uD83D\\uDC4D\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-semibold text-white mb-2\",\n                                            children: \"Vote en Temps R\\xe9el\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Votez avec des emojis pour influencer l'ordre de la playlist\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Music_Plus_QrCode_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-semibold text-white mb-2\",\n                                            children: \"Acc\\xe8s Facile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Rejoignez rapidement avec un code ou un QR code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SimpleToast.tsx":
/*!****************************************!*\
  !*** ./src/components/SimpleToast.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleToastProvider: () => (/* binding */ SimpleToastProvider),\n/* harmony export */   useSimpleToast: () => (/* binding */ useSimpleToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ SimpleToastProvider,useSimpleToast auto */ \n\n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Provider pour les toasts\nfunction SimpleToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (toast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            ...toast,\n            id\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto-remove après la durée spécifiée\n        if (toast.duration !== 0) {\n            setTimeout(()=>{\n                removeToast(id);\n            }, toast.duration || 4000);\n        }\n        return id;\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const success = (title, message)=>addToast({\n            type: \"success\",\n            title,\n            message\n        });\n    const error = (title, message)=>addToast({\n            type: \"error\",\n            title,\n            message\n        });\n    const warning = (title, message)=>addToast({\n            type: \"warning\",\n            title,\n            message\n        });\n    const info = (title, message)=>addToast({\n            type: \"info\",\n            title,\n            message\n        });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            addToast,\n            removeToast,\n            success,\n            error,\n            warning,\n            info\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleToastContainer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n// Hook pour utiliser les toasts\nfunction useSimpleToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error(\"useSimpleToast must be used within a SimpleToastProvider\");\n    }\n    return context;\n}\n// Composant Toast individuel\nfunction SimpleToast({ toast }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLeaving, setIsLeaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { removeToast } = useSimpleToast();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>setIsVisible(true), 100);\n        return ()=>clearTimeout(timer);\n    }, []);\n    const handleClose = ()=>{\n        setIsLeaving(true);\n        setTimeout(()=>{\n            removeToast(toast.id);\n        }, 300);\n    };\n    const getIcon = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 16\n                }, this);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getColors = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"bg-green-500 border-green-600 text-white\";\n            case \"error\":\n                return \"bg-red-500 border-red-600 text-white\";\n            case \"warning\":\n                return \"bg-yellow-500 border-yellow-600 text-white\";\n            case \"info\":\n                return \"bg-blue-500 border-blue-600 text-white\";\n            default:\n                return \"bg-gray-500 border-gray-600 text-white\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-start space-x-3 p-4 rounded-lg shadow-lg border backdrop-blur-lg transition-all duration-300 transform\", getColors(), {\n            \"translate-x-0 opacity-100\": isVisible && !isLeaving,\n            \"translate-x-full opacity-0\": !isVisible || isLeaving\n        }),\n        style: {\n            minWidth: \"300px\",\n            maxWidth: \"400px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-0.5\",\n                children: getIcon()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-semibold text-sm\",\n                        children: toast.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    toast.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm opacity-90 mt-1\",\n                        children: toast.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleClose,\n                className: \"flex-shrink-0 p-1 hover:bg-white/20 rounded transition-colors\",\n                \"aria-label\": \"Fermer la notification\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n// Container pour afficher les toasts\nfunction SimpleToastContainer() {\n    const { toasts } = useSimpleToast();\n    if (toasts.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleToast, {\n                toast: toast\n            }, toast.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\components\\\\SimpleToast.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TaW1wbGVUb2FzdC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUV1RTtBQUNTO0FBQ3BEO0FBcUI1QixNQUFNVSw2QkFBZVIsb0RBQWFBLENBQStCUztBQUVqRSwyQkFBMkI7QUFDcEIsU0FBU0Msb0JBQW9CLEVBQUVDLFFBQVEsRUFBaUM7SUFDN0UsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdmLCtDQUFRQSxDQUFxQixFQUFFO0lBRTNELE1BQU1nQixXQUFXLENBQUNDO1FBQ2hCLE1BQU1DLEtBQUtDLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHO1FBQ2hELE1BQU1DLFdBQTZCO1lBQUUsR0FBR04sS0FBSztZQUFFQztRQUFHO1FBRWxESCxVQUFVUyxDQUFBQSxPQUFRO21CQUFJQTtnQkFBTUQ7YUFBUztRQUVyQyx1Q0FBdUM7UUFDdkMsSUFBSU4sTUFBTVEsUUFBUSxLQUFLLEdBQUc7WUFDeEJDLFdBQVc7Z0JBQ1RDLFlBQVlUO1lBQ2QsR0FBR0QsTUFBTVEsUUFBUSxJQUFJO1FBQ3ZCO1FBRUEsT0FBT1A7SUFDVDtJQUVBLE1BQU1TLGNBQWMsQ0FBQ1Q7UUFDbkJILFVBQVVTLENBQUFBLE9BQVFBLEtBQUtJLE1BQU0sQ0FBQ1gsQ0FBQUEsUUFBU0EsTUFBTUMsRUFBRSxLQUFLQTtJQUN0RDtJQUVBLE1BQU1XLFVBQVUsQ0FBQ0MsT0FBZUMsVUFDOUJmLFNBQVM7WUFBRWdCLE1BQU07WUFBV0Y7WUFBT0M7UUFBUTtJQUU3QyxNQUFNRSxRQUFRLENBQUNILE9BQWVDLFVBQzVCZixTQUFTO1lBQUVnQixNQUFNO1lBQVNGO1lBQU9DO1FBQVE7SUFFM0MsTUFBTUcsVUFBVSxDQUFDSixPQUFlQyxVQUM5QmYsU0FBUztZQUFFZ0IsTUFBTTtZQUFXRjtZQUFPQztRQUFRO0lBRTdDLE1BQU1JLE9BQU8sQ0FBQ0wsT0FBZUMsVUFDM0JmLFNBQVM7WUFBRWdCLE1BQU07WUFBUUY7WUFBT0M7UUFBUTtJQUUxQyxxQkFDRSw4REFBQ3JCLGFBQWEwQixRQUFRO1FBQUNDLE9BQU87WUFDNUJ2QjtZQUNBRTtZQUNBVztZQUNBRTtZQUNBSTtZQUNBQztZQUNBQztRQUNGOztZQUNHdEI7MEJBQ0QsOERBQUN5Qjs7Ozs7Ozs7Ozs7QUFHUDtBQUVBLGdDQUFnQztBQUN6QixTQUFTQztJQUNkLE1BQU1DLFVBQVVyQyxpREFBVUEsQ0FBQ087SUFDM0IsSUFBSSxDQUFDOEIsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1Q7QUFFQSw2QkFBNkI7QUFDN0IsU0FBU0UsWUFBWSxFQUFFekIsS0FBSyxFQUErQjtJQUN6RCxNQUFNLENBQUMwQixXQUFXQyxhQUFhLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUM2QyxXQUFXQyxhQUFhLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLEVBQUUyQixXQUFXLEVBQUUsR0FBR1k7SUFFeEJ0QyxnREFBU0EsQ0FBQztRQUNSLE1BQU04QyxRQUFRckIsV0FBVyxJQUFNa0IsYUFBYSxPQUFPO1FBQ25ELE9BQU8sSUFBTUksYUFBYUQ7SUFDNUIsR0FBRyxFQUFFO0lBRUwsTUFBTUUsY0FBYztRQUNsQkgsYUFBYTtRQUNicEIsV0FBVztZQUNUQyxZQUFZVixNQUFNQyxFQUFFO1FBQ3RCLEdBQUc7SUFDTDtJQUVBLE1BQU1nQyxVQUFVO1FBQ2QsT0FBUWpDLE1BQU1lLElBQUk7WUFDaEIsS0FBSztnQkFDSCxxQkFBTyw4REFBQzNCLHdIQUFXQTtvQkFBQzhDLFdBQVU7Ozs7OztZQUNoQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDN0Msd0hBQVdBO29CQUFDNkMsV0FBVTs7Ozs7O1lBQ2hDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUMzQyx3SEFBYUE7b0JBQUMyQyxXQUFVOzs7Ozs7WUFDbEMsS0FBSztnQkFDSCxxQkFBTyw4REFBQzVDLHdIQUFJQTtvQkFBQzRDLFdBQVU7Ozs7OztZQUN6QjtnQkFDRSxxQkFBTyw4REFBQzVDLHdIQUFJQTtvQkFBQzRDLFdBQVU7Ozs7OztRQUMzQjtJQUNGO0lBRUEsTUFBTUMsWUFBWTtRQUNoQixPQUFRbkMsTUFBTWUsSUFBSTtZQUNoQixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDcUI7UUFDQ0YsV0FBVzFDLDBDQUFJQSxDQUNiLHFIQUNBMkMsYUFDQTtZQUNFLDZCQUE2QlQsYUFBYSxDQUFDRTtZQUMzQyw4QkFBOEIsQ0FBQ0YsYUFBYUU7UUFDOUM7UUFFRlMsT0FBTztZQUFFQyxVQUFVO1lBQVNDLFVBQVU7UUFBUTs7MEJBRTlDLDhEQUFDSDtnQkFBSUYsV0FBVTswQkFDWkQ7Ozs7OzswQkFHSCw4REFBQ0c7Z0JBQUlGLFdBQVU7O2tDQUNiLDhEQUFDTTt3QkFBR04sV0FBVTtrQ0FBeUJsQyxNQUFNYSxLQUFLOzs7Ozs7b0JBQ2pEYixNQUFNYyxPQUFPLGtCQUNaLDhEQUFDMkI7d0JBQUVQLFdBQVU7a0NBQTJCbEMsTUFBTWMsT0FBTzs7Ozs7Ozs7Ozs7OzBCQUl6RCw4REFBQzRCO2dCQUNDQyxTQUFTWDtnQkFDVEUsV0FBVTtnQkFDVlUsY0FBVzswQkFFWCw0RUFBQ3pELHdIQUFDQTtvQkFBQytDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSXJCO0FBRUEscUNBQXFDO0FBQ3JDLFNBQVNiO0lBQ1AsTUFBTSxFQUFFeEIsTUFBTSxFQUFFLEdBQUd5QjtJQUVuQixJQUFJekIsT0FBT2dELE1BQU0sS0FBSyxHQUFHLE9BQU87SUFFaEMscUJBQ0UsOERBQUNUO1FBQUlGLFdBQVU7a0JBQ1pyQyxPQUFPaUQsR0FBRyxDQUFDLENBQUM5QyxzQkFDWCw4REFBQ3lCO2dCQUEyQnpCLE9BQU9BO2VBQWpCQSxNQUFNQyxFQUFFOzs7Ozs7Ozs7O0FBSWxDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGxheWhpZnktdWkvLi9zcmMvY29tcG9uZW50cy9TaW1wbGVUb2FzdC50c3g/MTRkYyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBYLCBDaGVja0NpcmNsZSwgQWxlcnRDaXJjbGUsIEluZm8sIEFsZXJ0VHJpYW5nbGUgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgY2xzeCB9IGZyb20gJ2Nsc3gnO1xuXG5leHBvcnQgaW50ZXJmYWNlIFNpbXBsZVRvYXN0UHJvcHMge1xuICBpZDogc3RyaW5nO1xuICB0eXBlOiAnc3VjY2VzcycgfCAnZXJyb3InIHwgJ3dhcm5pbmcnIHwgJ2luZm8nO1xuICB0aXRsZTogc3RyaW5nO1xuICBtZXNzYWdlPzogc3RyaW5nO1xuICBkdXJhdGlvbj86IG51bWJlcjtcbn1cblxuLy8gQ29udGV4dGUgcG91ciBsZXMgdG9hc3RzXG5pbnRlcmZhY2UgVG9hc3RDb250ZXh0VHlwZSB7XG4gIHRvYXN0czogU2ltcGxlVG9hc3RQcm9wc1tdO1xuICBhZGRUb2FzdDogKHRvYXN0OiBPbWl0PFNpbXBsZVRvYXN0UHJvcHMsICdpZCc+KSA9PiBzdHJpbmc7XG4gIHJlbW92ZVRvYXN0OiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgc3VjY2VzczogKHRpdGxlOiBzdHJpbmcsIG1lc3NhZ2U/OiBzdHJpbmcpID0+IHN0cmluZztcbiAgZXJyb3I6ICh0aXRsZTogc3RyaW5nLCBtZXNzYWdlPzogc3RyaW5nKSA9PiBzdHJpbmc7XG4gIHdhcm5pbmc6ICh0aXRsZTogc3RyaW5nLCBtZXNzYWdlPzogc3RyaW5nKSA9PiBzdHJpbmc7XG4gIGluZm86ICh0aXRsZTogc3RyaW5nLCBtZXNzYWdlPzogc3RyaW5nKSA9PiBzdHJpbmc7XG59XG5cbmNvbnN0IFRvYXN0Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8VG9hc3RDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcblxuLy8gUHJvdmlkZXIgcG91ciBsZXMgdG9hc3RzXG5leHBvcnQgZnVuY3Rpb24gU2ltcGxlVG9hc3RQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IFt0b2FzdHMsIHNldFRvYXN0c10gPSB1c2VTdGF0ZTxTaW1wbGVUb2FzdFByb3BzW10+KFtdKTtcblxuICBjb25zdCBhZGRUb2FzdCA9ICh0b2FzdDogT21pdDxTaW1wbGVUb2FzdFByb3BzLCAnaWQnPikgPT4ge1xuICAgIGNvbnN0IGlkID0gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpO1xuICAgIGNvbnN0IG5ld1RvYXN0OiBTaW1wbGVUb2FzdFByb3BzID0geyAuLi50b2FzdCwgaWQgfTtcbiAgICBcbiAgICBzZXRUb2FzdHMocHJldiA9PiBbLi4ucHJldiwgbmV3VG9hc3RdKTtcbiAgICBcbiAgICAvLyBBdXRvLXJlbW92ZSBhcHLDqHMgbGEgZHVyw6llIHNww6ljaWZpw6llXG4gICAgaWYgKHRvYXN0LmR1cmF0aW9uICE9PSAwKSB7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgcmVtb3ZlVG9hc3QoaWQpO1xuICAgICAgfSwgdG9hc3QuZHVyYXRpb24gfHwgNDAwMCk7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBpZDtcbiAgfTtcblxuICBjb25zdCByZW1vdmVUb2FzdCA9IChpZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0VG9hc3RzKHByZXYgPT4gcHJldi5maWx0ZXIodG9hc3QgPT4gdG9hc3QuaWQgIT09IGlkKSk7XG4gIH07XG5cbiAgY29uc3Qgc3VjY2VzcyA9ICh0aXRsZTogc3RyaW5nLCBtZXNzYWdlPzogc3RyaW5nKSA9PiBcbiAgICBhZGRUb2FzdCh7IHR5cGU6ICdzdWNjZXNzJywgdGl0bGUsIG1lc3NhZ2UgfSk7XG4gIFxuICBjb25zdCBlcnJvciA9ICh0aXRsZTogc3RyaW5nLCBtZXNzYWdlPzogc3RyaW5nKSA9PiBcbiAgICBhZGRUb2FzdCh7IHR5cGU6ICdlcnJvcicsIHRpdGxlLCBtZXNzYWdlIH0pO1xuICBcbiAgY29uc3Qgd2FybmluZyA9ICh0aXRsZTogc3RyaW5nLCBtZXNzYWdlPzogc3RyaW5nKSA9PiBcbiAgICBhZGRUb2FzdCh7IHR5cGU6ICd3YXJuaW5nJywgdGl0bGUsIG1lc3NhZ2UgfSk7XG4gIFxuICBjb25zdCBpbmZvID0gKHRpdGxlOiBzdHJpbmcsIG1lc3NhZ2U/OiBzdHJpbmcpID0+IFxuICAgIGFkZFRvYXN0KHsgdHlwZTogJ2luZm8nLCB0aXRsZSwgbWVzc2FnZSB9KTtcblxuICByZXR1cm4gKFxuICAgIDxUb2FzdENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3tcbiAgICAgIHRvYXN0cyxcbiAgICAgIGFkZFRvYXN0LFxuICAgICAgcmVtb3ZlVG9hc3QsXG4gICAgICBzdWNjZXNzLFxuICAgICAgZXJyb3IsXG4gICAgICB3YXJuaW5nLFxuICAgICAgaW5mbyxcbiAgICB9fT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDxTaW1wbGVUb2FzdENvbnRhaW5lciAvPlxuICAgIDwvVG9hc3RDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuXG4vLyBIb29rIHBvdXIgdXRpbGlzZXIgbGVzIHRvYXN0c1xuZXhwb3J0IGZ1bmN0aW9uIHVzZVNpbXBsZVRvYXN0KCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChUb2FzdENvbnRleHQpO1xuICBpZiAoIWNvbnRleHQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVNpbXBsZVRvYXN0IG11c3QgYmUgdXNlZCB3aXRoaW4gYSBTaW1wbGVUb2FzdFByb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG5cbi8vIENvbXBvc2FudCBUb2FzdCBpbmRpdmlkdWVsXG5mdW5jdGlvbiBTaW1wbGVUb2FzdCh7IHRvYXN0IH06IHsgdG9hc3Q6IFNpbXBsZVRvYXN0UHJvcHMgfSkge1xuICBjb25zdCBbaXNWaXNpYmxlLCBzZXRJc1Zpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNMZWF2aW5nLCBzZXRJc0xlYXZpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCB7IHJlbW92ZVRvYXN0IH0gPSB1c2VTaW1wbGVUb2FzdCgpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHNldElzVmlzaWJsZSh0cnVlKSwgMTAwKTtcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGhhbmRsZUNsb3NlID0gKCkgPT4ge1xuICAgIHNldElzTGVhdmluZyh0cnVlKTtcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHJlbW92ZVRvYXN0KHRvYXN0LmlkKTtcbiAgICB9LCAzMDApO1xuICB9O1xuXG4gIGNvbnN0IGdldEljb24gPSAoKSA9PiB7XG4gICAgc3dpdGNoICh0b2FzdC50eXBlKSB7XG4gICAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgICAgcmV0dXJuIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz47XG4gICAgICBjYXNlICdlcnJvcic6XG4gICAgICAgIHJldHVybiA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+O1xuICAgICAgY2FzZSAnd2FybmluZyc6XG4gICAgICAgIHJldHVybiA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz47XG4gICAgICBjYXNlICdpbmZvJzpcbiAgICAgICAgcmV0dXJuIDxJbmZvIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPjtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiA8SW5mbyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz47XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldENvbG9ycyA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKHRvYXN0LnR5cGUpIHtcbiAgICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgICByZXR1cm4gJ2JnLWdyZWVuLTUwMCBib3JkZXItZ3JlZW4tNjAwIHRleHQtd2hpdGUnO1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ2JnLXJlZC01MDAgYm9yZGVyLXJlZC02MDAgdGV4dC13aGl0ZSc7XG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuICdiZy15ZWxsb3ctNTAwIGJvcmRlci15ZWxsb3ctNjAwIHRleHQtd2hpdGUnO1xuICAgICAgY2FzZSAnaW5mbyc6XG4gICAgICAgIHJldHVybiAnYmctYmx1ZS01MDAgYm9yZGVyLWJsdWUtNjAwIHRleHQtd2hpdGUnO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICdiZy1ncmF5LTUwMCBib3JkZXItZ3JheS02MDAgdGV4dC13aGl0ZSc7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtjbHN4KFxuICAgICAgICAnZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTMgcC00IHJvdW5kZWQtbGcgc2hhZG93LWxnIGJvcmRlciBiYWNrZHJvcC1ibHVyLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0cmFuc2Zvcm0nLFxuICAgICAgICBnZXRDb2xvcnMoKSxcbiAgICAgICAge1xuICAgICAgICAgICd0cmFuc2xhdGUteC0wIG9wYWNpdHktMTAwJzogaXNWaXNpYmxlICYmICFpc0xlYXZpbmcsXG4gICAgICAgICAgJ3RyYW5zbGF0ZS14LWZ1bGwgb3BhY2l0eS0wJzogIWlzVmlzaWJsZSB8fCBpc0xlYXZpbmcsXG4gICAgICAgIH1cbiAgICAgICl9XG4gICAgICBzdHlsZT17eyBtaW5XaWR0aDogJzMwMHB4JywgbWF4V2lkdGg6ICc0MDBweCcgfX1cbiAgICA+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgbXQtMC41XCI+XG4gICAgICAgIHtnZXRJY29uKCl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXNtXCI+e3RvYXN0LnRpdGxlfTwvaDQ+XG4gICAgICAgIHt0b2FzdC5tZXNzYWdlICYmIChcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIG9wYWNpdHktOTAgbXQtMVwiPnt0b2FzdC5tZXNzYWdlfTwvcD5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsb3NlfVxuICAgICAgICBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIHAtMSBob3ZlcjpiZy13aGl0ZS8yMCByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgYXJpYS1sYWJlbD1cIkZlcm1lciBsYSBub3RpZmljYXRpb25cIlxuICAgICAgPlxuICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgIDwvYnV0dG9uPlxuICAgIDwvZGl2PlxuICApO1xufVxuXG4vLyBDb250YWluZXIgcG91ciBhZmZpY2hlciBsZXMgdG9hc3RzXG5mdW5jdGlvbiBTaW1wbGVUb2FzdENvbnRhaW5lcigpIHtcbiAgY29uc3QgeyB0b2FzdHMgfSA9IHVzZVNpbXBsZVRvYXN0KCk7XG5cbiAgaWYgKHRvYXN0cy5sZW5ndGggPT09IDApIHJldHVybiBudWxsO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtNCByaWdodC00IHotNTAgc3BhY2UteS0yXCI+XG4gICAgICB7dG9hc3RzLm1hcCgodG9hc3QpID0+IChcbiAgICAgICAgPFNpbXBsZVRvYXN0IGtleT17dG9hc3QuaWR9IHRvYXN0PXt0b2FzdH0gLz5cbiAgICAgICkpfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJYIiwiQ2hlY2tDaXJjbGUiLCJBbGVydENpcmNsZSIsIkluZm8iLCJBbGVydFRyaWFuZ2xlIiwiY2xzeCIsIlRvYXN0Q29udGV4dCIsInVuZGVmaW5lZCIsIlNpbXBsZVRvYXN0UHJvdmlkZXIiLCJjaGlsZHJlbiIsInRvYXN0cyIsInNldFRvYXN0cyIsImFkZFRvYXN0IiwidG9hc3QiLCJpZCIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsIm5ld1RvYXN0IiwicHJldiIsImR1cmF0aW9uIiwic2V0VGltZW91dCIsInJlbW92ZVRvYXN0IiwiZmlsdGVyIiwic3VjY2VzcyIsInRpdGxlIiwibWVzc2FnZSIsInR5cGUiLCJlcnJvciIsIndhcm5pbmciLCJpbmZvIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsIlNpbXBsZVRvYXN0Q29udGFpbmVyIiwidXNlU2ltcGxlVG9hc3QiLCJjb250ZXh0IiwiRXJyb3IiLCJTaW1wbGVUb2FzdCIsImlzVmlzaWJsZSIsInNldElzVmlzaWJsZSIsImlzTGVhdmluZyIsInNldElzTGVhdmluZyIsInRpbWVyIiwiY2xlYXJUaW1lb3V0IiwiaGFuZGxlQ2xvc2UiLCJnZXRJY29uIiwiY2xhc3NOYW1lIiwiZ2V0Q29sb3JzIiwiZGl2Iiwic3R5bGUiLCJtaW5XaWR0aCIsIm1heFdpZHRoIiwiaDQiLCJwIiwiYnV0dG9uIiwib25DbGljayIsImFyaWEtbGFiZWwiLCJsZW5ndGgiLCJtYXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SimpleToast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/AppContext.tsx":
/*!************************************!*\
  !*** ./src/context/AppContext.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProvider: () => (/* binding */ AppProvider),\n/* harmony export */   useApp: () => (/* binding */ useApp),\n/* harmony export */   useAppState: () => (/* binding */ useAppState),\n/* harmony export */   useCurrentSession: () => (/* binding */ useCurrentSession),\n/* harmony export */   useCurrentUser: () => (/* binding */ useCurrentUser),\n/* harmony export */   usePreferences: () => (/* binding */ usePreferences)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AppProvider,useApp,usePreferences,useCurrentUser,useCurrentSession,useAppState auto */ \n\n// État initial\nconst initialState = {\n    currentUser: null,\n    currentSession: null,\n    isLoading: false,\n    error: null,\n    preferences: {\n        theme: \"dark\",\n        notifications: true,\n        volume: 75\n    }\n};\n// Reducer\nfunction appReducer(state, action) {\n    switch(action.type){\n        case \"SET_USER\":\n            return {\n                ...state,\n                currentUser: action.payload\n            };\n        case \"SET_SESSION\":\n            return {\n                ...state,\n                currentSession: action.payload\n            };\n        case \"SET_LOADING\":\n            return {\n                ...state,\n                isLoading: action.payload\n            };\n        case \"SET_ERROR\":\n            return {\n                ...state,\n                error: action.payload,\n                isLoading: false\n            };\n        case \"UPDATE_PREFERENCES\":\n            return {\n                ...state,\n                preferences: {\n                    ...state.preferences,\n                    ...action.payload\n                }\n            };\n        case \"CLEAR_ALL\":\n            return {\n                ...initialState,\n                preferences: state.preferences\n            };\n        default:\n            return state;\n    }\n}\n// Contexte\nconst AppContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AppProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(appReducer, initialState);\n    // Charger les préférences depuis le localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, []);\n    // Sauvegarder les préférences dans le localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        state.preferences\n    ]);\n    // Actions de convenance\n    const setUser = (user)=>{\n        dispatch({\n            type: \"SET_USER\",\n            payload: user\n        });\n    };\n    const setSession = (session)=>{\n        dispatch({\n            type: \"SET_SESSION\",\n            payload: session\n        });\n    };\n    const setLoading = (loading)=>{\n        dispatch({\n            type: \"SET_LOADING\",\n            payload: loading\n        });\n    };\n    const setError = (error)=>{\n        dispatch({\n            type: \"SET_ERROR\",\n            payload: error\n        });\n    };\n    const updatePreferences = (preferences)=>{\n        dispatch({\n            type: \"UPDATE_PREFERENCES\",\n            payload: preferences\n        });\n    };\n    const clearAll = ()=>{\n        dispatch({\n            type: \"CLEAR_ALL\"\n        });\n    };\n    // Gestion du thème\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        state.preferences.theme\n    ]);\n    const contextValue = {\n        state,\n        dispatch,\n        setUser,\n        setSession,\n        setLoading,\n        setError,\n        updatePreferences,\n        clearAll\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\context\\\\AppContext.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n// Hook pour utiliser le contexte\nfunction useApp() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext);\n    if (context === undefined) {\n        throw new Error(\"useApp must be used within an AppProvider\");\n    }\n    return context;\n}\n// Hook pour les préférences utilisateur\nfunction usePreferences() {\n    const { state, updatePreferences } = useApp();\n    return {\n        preferences: state.preferences,\n        updatePreferences,\n        setTheme: (theme)=>updatePreferences({\n                theme\n            }),\n        setNotifications: (notifications)=>updatePreferences({\n                notifications\n            }),\n        setVolume: (volume)=>updatePreferences({\n                volume\n            })\n    };\n}\n// Hook pour l'utilisateur actuel\nfunction useCurrentUser() {\n    const { state, setUser } = useApp();\n    return {\n        user: state.currentUser,\n        setUser,\n        isLoggedIn: !!state.currentUser,\n        isHost: state.currentUser?.isHost || false\n    };\n}\n// Hook pour la session actuelle\nfunction useCurrentSession() {\n    const { state, setSession } = useApp();\n    return {\n        session: state.currentSession,\n        setSession,\n        isInSession: !!state.currentSession,\n        sessionCode: state.currentSession?.code\n    };\n}\n// Hook pour l'état de chargement et les erreurs\nfunction useAppState() {\n    const { state, setLoading, setError } = useApp();\n    return {\n        isLoading: state.isLoading,\n        error: state.error,\n        setLoading,\n        setError,\n        clearError: ()=>setError(null)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/AppContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fbdbe73a68b9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGxheWhpZnktdWkvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzVjMjYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmYmRiZTczYTY4YjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _context_AppContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AppContext */ \"(rsc)/./src/context/AppContext.tsx\");\n/* harmony import */ var _components_SimpleToast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SimpleToast */ \"(rsc)/./src/components/SimpleToast.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"PlayHify - Playlist Collaborative\",\n    description: \"Cr\\xe9ez des playlists collaboratives en temps r\\xe9el pour vos \\xe9v\\xe9nements\",\n    keywords: \"playlist, collaborative, musique, spotify, temps r\\xe9el, \\xe9v\\xe9nement\",\n    authors: [\n        {\n            name: \"PlayHify Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    themeColor: \"#10b981\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 min-h-screen`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleToast__WEBPACK_IMPORTED_MODULE_3__.SimpleToastProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AppContext__WEBPACK_IMPORTED_MODULE_2__.AppProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Cline\\\\playhifyy\\\\playhify-ui\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Cline\playhifyy\playhify-ui\src\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/SimpleToast.tsx":
/*!****************************************!*\
  !*** ./src/components/SimpleToast.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SimpleToastProvider: () => (/* binding */ e0),
/* harmony export */   useSimpleToast: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Cline\playhifyy\playhify-ui\src\components\SimpleToast.tsx#SimpleToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Cline\playhifyy\playhify-ui\src\components\SimpleToast.tsx#useSimpleToast`);


/***/ }),

/***/ "(rsc)/./src/context/AppContext.tsx":
/*!************************************!*\
  !*** ./src/context/AppContext.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppProvider: () => (/* binding */ e0),
/* harmony export */   useApp: () => (/* binding */ e1),
/* harmony export */   useAppState: () => (/* binding */ e5),
/* harmony export */   useCurrentSession: () => (/* binding */ e4),
/* harmony export */   useCurrentUser: () => (/* binding */ e3),
/* harmony export */   usePreferences: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Cline\playhifyy\playhify-ui\src\context\AppContext.tsx#AppProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Cline\playhifyy\playhify-ui\src\context\AppContext.tsx#useApp`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Cline\playhifyy\playhify-ui\src\context\AppContext.tsx#usePreferences`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Cline\playhifyy\playhify-ui\src\context\AppContext.tsx#useCurrentUser`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Cline\playhifyy\playhify-ui\src\context\AppContext.tsx#useCurrentSession`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Cline\playhifyy\playhify-ui\src\context\AppContext.tsx#useAppState`);


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wbGF5aGlmeS11aS8uL3NyYy9hcHAvZmF2aWNvbi5pY28/MmEwNSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMAISON%5CDocuments%5CCline%5Cplayhifyy%5Cplayhify-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMAISON%5CDocuments%5CCline%5Cplayhifyy%5Cplayhify-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();