# Guide de Développement - PlayHify

Ce document contient toutes les informations nécessaires pour développer et contribuer au projet PlayHify.

## 🏗️ Architecture

### Structure du Projet

```
playhify-ui/
├── src/
│   ├── app/                    # Pages Next.js (App Router)
│   │   ├── create-session/     # Page de création de session
│   │   ├── session/[code]/     # Pages de session dynamiques
│   │   │   ├── page.tsx        # Vue participant
│   │   │   └── host/page.tsx   # Vue hôte
│   │   ├── layout.tsx          # Layout principal
│   │   └── page.tsx            # Page d'accueil
│   ├── components/             # Composants réutilisables
│   │   ├── SongCard.tsx        # Carte de musique avec votes
│   │   ├── MusicSearch.tsx     # Recherche Spotify
│   │   ├── PlayerControls.tsx  # Contrôles de lecture
│   │   ├── Toast.tsx           # Système de notifications
│   │   ├── Loading.tsx         # États de chargement
│   │   ├── EmptyState.tsx      # États vides
│   │   └── Modal.tsx           # Modals réutilisables
│   ├── context/                # Contextes React
│   │   └── AppContext.tsx      # État global de l'application
│   ├── hooks/                  # Hooks personnalisés
│   │   ├── useSession.ts       # Gestion des sessions WebSocket
│   │   └── useSpotify.ts       # Intégration Spotify
│   ├── types/                  # Types TypeScript
│   │   └── index.ts            # Définitions de types
│   └── config/                 # Configuration
│       └── constants.ts        # Constantes de l'application
├── public/                     # Fichiers statiques
├── .env.example               # Variables d'environnement exemple
└── package.json
```

### Technologies Utilisées

- **Next.js 15** - Framework React avec App Router
- **TypeScript** - Typage statique
- **Tailwind CSS** - Framework CSS utilitaire
- **Socket.IO Client** - Communication temps réel
- **Supabase** - Base de données et authentification
- **Spotify Web API** - Intégration musicale

## 🚀 Configuration de l'Environnement

### Prérequis

- Node.js 18+ 
- npm ou yarn
- Compte Spotify Developer
- Projet Supabase

### Installation

1. **Cloner le repository**
```bash
git clone https://github.com/votre-username/playhify.git
cd playhify/playhify-ui
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configuration des variables d'environnement**
```bash
cp .env.example .env.local
```

Remplir les variables dans `.env.local` :
```env
NEXT_PUBLIC_SPOTIFY_CLIENT_ID=your_spotify_client_id
NEXT_PUBLIC_SPOTIFY_REDIRECT_URI=http://localhost:3000/auth/spotify/callback
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

### Configuration Spotify

1. Aller sur [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Créer une nouvelle application
3. Ajouter `http://localhost:3000/auth/spotify/callback` dans les Redirect URIs
4. Copier le Client ID dans `.env.local`

### Configuration Supabase

1. Créer un nouveau projet sur [Supabase](https://supabase.com)
2. Copier l'URL et la clé anonyme dans `.env.local`
3. Configurer les tables (voir section Base de Données)

## 📊 Base de Données

### Schéma Supabase

```sql
-- Sessions
CREATE TABLE sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  code VARCHAR(6) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  host_id UUID REFERENCES users(id),
  is_active BOOLEAN DEFAULT true,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id UUID REFERENCES sessions(id),
  nickname VARCHAR(50) NOT NULL,
  is_host BOOLEAN DEFAULT false,
  points INTEGER DEFAULT 0,
  is_online BOOLEAN DEFAULT true,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Songs
CREATE TABLE songs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id UUID REFERENCES sessions(id),
  spotify_id VARCHAR(100) NOT NULL,
  title VARCHAR(200) NOT NULL,
  artist VARCHAR(200) NOT NULL,
  album VARCHAR(200),
  duration INTEGER NOT NULL,
  album_cover TEXT,
  submitted_by UUID REFERENCES users(id),
  position INTEGER DEFAULT 0,
  is_playing BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Votes
CREATE TABLE votes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  song_id UUID REFERENCES songs(id),
  user_id UUID REFERENCES users(id),
  emoji VARCHAR(10) NOT NULL,
  value INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(song_id, user_id, emoji)
);
```

## 🎨 Conventions de Code

### TypeScript

- Utiliser des interfaces pour les types d'objets
- Préférer `type` pour les unions et les primitives
- Exporter les types depuis `src/types/index.ts`

### React

- Utiliser des composants fonctionnels avec hooks
- Préférer les hooks personnalisés pour la logique réutilisable
- Utiliser `'use client'` pour les composants interactifs

### Styling

- Utiliser Tailwind CSS pour le styling
- Créer des classes utilitaires dans `globals.css` si nécessaire
- Utiliser `clsx` pour les classes conditionnelles

### Nommage

- **Fichiers** : PascalCase pour les composants, camelCase pour les utilitaires
- **Variables** : camelCase
- **Constantes** : UPPER_SNAKE_CASE
- **Types** : PascalCase

## 🔧 Scripts de Développement

```bash
# Développement
npm run dev

# Build de production
npm run build

# Démarrer en production
npm start

# Linting
npm run lint

# Vérification des types
npm run type-check

# Formatage du code
npm run format
```

## 🧪 Tests

### Structure des Tests

```
src/
├── __tests__/          # Tests unitaires
├── components/
│   └── __tests__/      # Tests de composants
└── hooks/
    └── __tests__/      # Tests de hooks
```

### Commandes de Test

```bash
# Lancer tous les tests
npm test

# Tests en mode watch
npm run test:watch

# Coverage
npm run test:coverage
```

## 🚀 Déploiement

### Vercel (Recommandé)

1. Connecter le repository à Vercel
2. Configurer les variables d'environnement
3. Déployer automatiquement sur push

### Variables d'Environnement de Production

```env
NEXT_PUBLIC_SPOTIFY_CLIENT_ID=prod_spotify_client_id
NEXT_PUBLIC_SPOTIFY_REDIRECT_URI=https://yourdomain.com/auth/spotify/callback
NEXT_PUBLIC_SUPABASE_URL=prod_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=prod_supabase_anon_key
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api
```

## 🐛 Debugging

### Outils de Debug

- **React Developer Tools** - Extension navigateur
- **Next.js DevTools** - Intégré dans le navigateur
- **Console Logs** - Utiliser `console.log` avec parcimonie

### Problèmes Courants

1. **Erreurs de Hydration**
   - Vérifier les différences entre SSR et client
   - Utiliser `useEffect` pour le code client uniquement

2. **Erreurs WebSocket**
   - Vérifier la connexion réseau
   - Implémenter la reconnexion automatique

3. **Erreurs Spotify**
   - Vérifier les tokens d'accès
   - Gérer l'expiration des tokens

## 📝 Contribution

### Workflow Git

1. Créer une branche feature : `git checkout -b feature/nom-feature`
2. Faire les modifications
3. Committer : `git commit -m "feat: description"`
4. Pousser : `git push origin feature/nom-feature`
5. Créer une Pull Request

### Convention de Commit

Utiliser [Conventional Commits](https://www.conventionalcommits.org/) :

- `feat:` - Nouvelle fonctionnalité
- `fix:` - Correction de bug
- `docs:` - Documentation
- `style:` - Formatage, point-virgules manquants, etc.
- `refactor:` - Refactoring de code
- `test:` - Ajout de tests
- `chore:` - Maintenance

### Code Review

- Vérifier la conformité aux conventions
- Tester les nouvelles fonctionnalités
- Vérifier les performances
- S'assurer de la compatibilité mobile

## 📚 Ressources

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Spotify Web API](https://developer.spotify.com/documentation/web-api)
- [Supabase Documentation](https://supabase.com/docs)
- [Socket.IO Documentation](https://socket.io/docs/v4)
