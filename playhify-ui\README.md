# PlayHify - Application de Playlist Collaborative

PlayHify est une application web moderne permettant de créer des playlists collaboratives en temps réel pour vos événements. Laissez vos invités ajouter leurs musiques préférées et voter pour leurs titres favoris !

## 🎵 Fonctionnalités

### Pour l'Hôte
- **Création de session** avec code d'invitation unique
- **Contrôles de lecture complets** (play, pause, suivant, précédent, volume)
- **Tableau de bord de gestion** avec statistiques en temps réel
- **Modération de la playlist** (supprimer des titres)
- **Intégration Spotify** pour la lecture et l'exportation

### Pour les Participants
- **Accès facile** via code de session ou QR code
- **Recherche et ajout de musiques** depuis Spotify
- **Système de vote avec emojis** (👍, 🔥, ❤️, 👎)
- **Visualisation en temps réel** de la playlist et des votes
- **Système de points et gamification**

### Fonctionnalités Techniques
- **Synchronisation temps réel** via WebSockets
- **Interface responsive** adaptée mobile et desktop
- **Gestion d'état avancée** avec React Context
- **Notifications toast** pour les interactions
- **Thème sombre/clair** adaptatif

## 🚀 Technologies Utilisées

### Frontend
- **Next.js 15** - Framework React avec App Router
- **TypeScript** - Typage statique
- **Tailwind CSS** - Framework CSS utilitaire
- **Lucide React** - Icônes modernes
- **Socket.IO Client** - Communication temps réel

### Backend (à venir)
- **Node.js** avec Express
- **Socket.IO** - WebSockets
- **Supabase** - Base de données et authentification
- **Spotify Web API** - Intégration musicale

## 📦 Installation

1. **Cloner le repository**
```bash
git clone https://github.com/votre-username/playhify.git
cd playhify/playhify-ui
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configurer les variables d'environnement**
```bash
cp .env.example .env.local
```

Remplissez les variables suivantes :
```env
NEXT_PUBLIC_SPOTIFY_CLIENT_ID=your_spotify_client_id
NEXT_PUBLIC_SPOTIFY_REDIRECT_URI=http://localhost:3000/auth/spotify/callback
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

4. **Lancer le serveur de développement**
```bash
npm run dev
```

Ouvrez [http://localhost:3000](http://localhost:3000) dans votre navigateur.

## 🏗️ Structure du Projet

```
playhify-ui/
├── src/
│   ├── app/                    # Pages Next.js (App Router)
│   │   ├── create-session/     # Page de création de session
│   │   ├── session/[code]/     # Pages de session dynamiques
│   │   └── layout.tsx          # Layout principal
│   ├── components/             # Composants réutilisables
│   │   ├── SongCard.tsx        # Carte de musique
│   │   ├── MusicSearch.tsx     # Recherche de musiques
│   │   ├── PlayerControls.tsx  # Contrôles de lecture
│   │   └── Toast.tsx           # Notifications
│   ├── context/                # Contextes React
│   │   └── AppContext.tsx      # État global de l'application
│   ├── hooks/                  # Hooks personnalisés
│   │   ├── useSession.ts       # Gestion des sessions WebSocket
│   │   └── useSpotify.ts       # Intégration Spotify
│   ├── types/                  # Types TypeScript
│   │   └── index.ts            # Définitions de types
│   └── config/                 # Configuration
│       └── constants.ts        # Constantes de l'application
├── public/                     # Fichiers statiques
└── package.json
```

## 🎮 Utilisation

### Créer une Session (Hôte)
1. Cliquez sur "Créer une Session"
2. Configurez les paramètres (nom, limite de musiques, vote)
3. Partagez le code de session avec vos invités
4. Gérez la playlist depuis le tableau de bord

### Rejoindre une Session (Participant)
1. Entrez le code de session reçu
2. Choisissez votre pseudo
3. Ajoutez vos musiques préférées
4. Votez pour influencer l'ordre de la playlist

## 🔧 Scripts Disponibles

```bash
# Développement
npm run dev

# Build de production
npm run build

# Démarrer en production
npm start

# Linting
npm run lint

# Type checking
npm run type-check
```

## 🌟 Fonctionnalités à Venir

- [ ] **Backend complet** avec API REST
- [ ] **Authentification utilisateur** via Supabase
- [ ] **Intégration Spotify complète** (lecture, playlists)
- [ ] **Chat en temps réel** dans les sessions
- [ ] **Historique des sessions**
- [ ] **Badges et achievements**
- [ ] **Mode hors ligne** avec cache
- [ ] **PWA** (Progressive Web App)
- [ ] **Tests automatisés**

## 🤝 Contribution

Les contributions sont les bienvenues ! Pour contribuer :

1. Fork le projet
2. Créez une branche feature (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 👥 Équipe

- **Développeur Principal** - [Votre Nom](https://github.com/votre-username)

## 🙏 Remerciements

- [Next.js](https://nextjs.org/) pour le framework
- [Tailwind CSS](https://tailwindcss.com/) pour le styling
- [Spotify Web API](https://developer.spotify.com/documentation/web-api/) pour l'intégration musicale
- [Supabase](https://supabase.com/) pour la base de données
- [Lucide](https://lucide.dev/) pour les icônes
