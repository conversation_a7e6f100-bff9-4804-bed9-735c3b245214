{"name": "playhify-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@headlessui/react": "^1.7.19", "@supabase/supabase-js": "^2.49.8", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "14.2.15", "react": "^18.3.1", "react-dom": "^18.3.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.0", "typescript": "^5"}}