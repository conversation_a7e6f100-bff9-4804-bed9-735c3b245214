'use client';

import { useState } from 'react';
import { Music, Settings, Users, QrCode, Copy, Check } from 'lucide-react';
import Link from 'next/link';

export default function CreateSession() {
  const [sessionName, setSessionName] = useState('');
  const [maxSongsPerUser, setMaxSongsPerUser] = useState(5);
  const [allowVoting, setAllowVoting] = useState(true);
  const [sessionCreated, setSessionCreated] = useState(false);
  const [sessionCode, setSessionCode] = useState('');
  const [copied, setCopied] = useState(false);

  const generateSessionCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const handleCreateSession = async () => {
    if (!sessionName.trim()) return;
    
    // Simuler la création de session
    const code = generateSessionCode();
    setSessionCode(code);
    setSessionCreated(true);
    
    // TODO: Intégrer avec l'API backend
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(sessionCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Erreur lors de la copie:', err);
    }
  };

  if (sessionCreated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-2xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <Link href="/" className="inline-flex items-center text-white/70 hover:text-white mb-6">
                ← Retour à l'accueil
              </Link>
              <div className="flex items-center justify-center mb-6">
                <Music className="w-12 h-12 text-white mr-4" />
                <h1 className="text-4xl font-bold text-white">Session Créée !</h1>
              </div>
            </div>

            {/* Session Info Card */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 mb-8">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-2">{sessionName}</h2>
                <p className="text-gray-300 mb-6">Votre session est prête ! Partagez le code avec vos invités.</p>
                
                {/* Session Code */}
                <div className="bg-white/20 rounded-lg p-6 mb-6">
                  <p className="text-gray-300 text-sm mb-2">Code de session</p>
                  <div className="flex items-center justify-center space-x-4">
                    <span className="text-4xl font-bold text-white tracking-wider">{sessionCode}</span>
                    <button
                      onClick={copyToClipboard}
                      className="p-2 bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors"
                    >
                      {copied ? <Check className="w-5 h-5 text-white" /> : <Copy className="w-5 h-5 text-white" />}
                    </button>
                  </div>
                </div>

                {/* QR Code Placeholder */}
                <div className="bg-white/20 rounded-lg p-6 mb-6">
                  <div className="w-32 h-32 bg-white rounded-lg mx-auto flex items-center justify-center">
                    <QrCode className="w-16 h-16 text-gray-400" />
                  </div>
                  <p className="text-gray-300 text-sm mt-2">QR Code pour rejoindre</p>
                </div>

                {/* Session Settings */}
                <div className="text-left bg-white/10 rounded-lg p-4 mb-6">
                  <h3 className="text-white font-semibold mb-2">Paramètres de la session</h3>
                  <ul className="text-gray-300 text-sm space-y-1">
                    <li>• Maximum {maxSongsPerUser} musiques par utilisateur</li>
                    <li>• Vote {allowVoting ? 'activé' : 'désactivé'}</li>
                  </ul>
                </div>

                {/* Actions */}
                <div className="space-y-4">
                  <Link
                    href={`/session/${sessionCode}/host`}
                    className="w-full inline-flex items-center justify-center px-6 py-3 bg-green-500 text-white rounded-lg font-semibold hover:bg-green-600 transition-colors"
                  >
                    <Music className="w-5 h-5 mr-2" />
                    Accéder au Tableau de Bord
                  </Link>
                  
                  <Link
                    href="/"
                    className="w-full inline-flex items-center justify-center px-6 py-3 bg-white/20 text-white rounded-lg font-semibold hover:bg-white/30 transition-colors"
                  >
                    Créer une Nouvelle Session
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <Link href="/" className="inline-flex items-center text-white/70 hover:text-white mb-6">
              ← Retour à l'accueil
            </Link>
            <div className="flex items-center justify-center mb-6">
              <Music className="w-12 h-12 text-white mr-4" />
              <h1 className="text-4xl font-bold text-white">Créer une Session</h1>
            </div>
            <p className="text-xl text-gray-300">
              Configurez votre session de playlist collaborative
            </p>
          </div>

          {/* Create Session Form */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
            <form onSubmit={(e) => { e.preventDefault(); handleCreateSession(); }} className="space-y-6">
              {/* Session Name */}
              <div>
                <label className="block text-white font-semibold mb-2">
                  Nom de la session *
                </label>
                <input
                  type="text"
                  value={sessionName}
                  onChange={(e) => setSessionName(e.target.value)}
                  placeholder="Ex: Soirée chez Marie, Réunion équipe..."
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  required
                />
              </div>

              {/* Settings */}
              <div className="space-y-4">
                <h3 className="text-white font-semibold flex items-center">
                  <Settings className="w-5 h-5 mr-2" />
                  Paramètres
                </h3>
                
                {/* Max Songs Per User */}
                <div>
                  <label className="block text-white font-medium mb-2">
                    Maximum de musiques par utilisateur
                  </label>
                  <select
                    value={maxSongsPerUser}
                    onChange={(e) => setMaxSongsPerUser(Number(e.target.value))}
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value={3}>3 musiques</option>
                    <option value={5}>5 musiques</option>
                    <option value={10}>10 musiques</option>
                    <option value={-1}>Illimité</option>
                  </select>
                </div>

                {/* Allow Voting */}
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="allowVoting"
                    checked={allowVoting}
                    onChange={(e) => setAllowVoting(e.target.checked)}
                    className="w-5 h-5 text-green-500 bg-white/20 border-white/30 rounded focus:ring-green-500 focus:ring-2"
                  />
                  <label htmlFor="allowVoting" className="text-white font-medium">
                    Permettre le vote sur les musiques
                  </label>
                </div>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={!sessionName.trim()}
                className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors ${
                  sessionName.trim()
                    ? 'bg-green-500 hover:bg-green-600 text-white'
                    : 'bg-gray-500 text-gray-300 cursor-not-allowed'
                }`}
              >
                <Users className="w-5 h-5 mr-2 inline" />
                Créer la Session
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
