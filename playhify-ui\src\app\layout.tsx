import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AppProvider } from "@/context/AppContext";
import { SimpleToastProvider } from "@/components/SimpleToast";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "PlayHify - Playlist Collaborative",
  description: "Créez des playlists collaboratives en temps réel pour vos événements",
  keywords: "playlist, collaborative, musique, spotify, temps réel, événement",
  authors: [{ name: "PlayHify Team" }],
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#10b981",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 min-h-screen`}
      >
        <SimpleToastProvider>
          <AppProvider>
            {children}
          </AppProvider>
        </SimpleToastProvider>
      </body>
    </html>
  );
}
