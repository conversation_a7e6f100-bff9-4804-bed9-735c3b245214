'use client';

import { useState } from 'react';
import { Music, Users, Plus, QrCode } from 'lucide-react';
import Link from 'next/link';
import { useSimpleToast } from '@/components/SimpleToast';

export default function Home() {
  const [sessionCode, setSessionCode] = useState('');
  const toast = useSimpleToast();

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <Music className="w-12 h-12 text-white mr-4" />
            <h1 className="text-5xl font-bold text-white">PlayHify</h1>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Créez des playlists collaboratives en temps réel pour vos événements.
            Laissez vos invités ajouter leurs musiques préférées et voter pour leurs titres favoris !
          </p>
        </div>

        {/* Main Actions */}
        <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
          {/* Create Session Card */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <Plus className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-white mb-4">Créer une Session</h2>
              <p className="text-gray-300 mb-6">
                Démarrez une nouvelle session de playlist collaborative.
                Vous aurez le contrôle total sur la lecture et pourrez modérer les ajouts.
              </p>
              <Link
                href="/create-session"
                className="inline-flex items-center px-6 py-3 bg-green-500 text-white rounded-lg font-semibold hover:bg-green-600 transition-colors"
              >
                <Plus className="w-5 h-5 mr-2" />
                Créer une Session
              </Link>
            </div>
          </div>

          {/* Join Session Card */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-white mb-4">Rejoindre une Session</h2>
              <p className="text-gray-300 mb-6">
                Participez à une session existante avec un code d'invitation.
                Ajoutez vos musiques et votez pour vos titres préférés !
              </p>

              <div className="space-y-4">
                <div>
                  <input
                    type="text"
                    placeholder="Code de session (ex: ABC123)"
                    value={sessionCode}
                    onChange={(e) => setSessionCode(e.target.value.toUpperCase())}
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    maxLength={6}
                  />
                </div>
                {sessionCode ? (
                  <Link
                    href={`/session/${sessionCode}`}
                    className="inline-flex items-center px-6 py-3 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-colors"
                  >
                    <Users className="w-5 h-5 mr-2" />
                    Rejoindre
                  </Link>
                ) : (
                  <button
                    onClick={() => toast.warning('Code requis', 'Veuillez entrer un code de session pour continuer')}
                    className="inline-flex items-center px-6 py-3 bg-gray-500 text-gray-300 rounded-lg font-semibold cursor-not-allowed"
                  >
                    <Users className="w-5 h-5 mr-2" />
                    Rejoindre
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mt-20 max-w-6xl mx-auto">
          <h3 className="text-3xl font-bold text-white text-center mb-12">Fonctionnalités</h3>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Music className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-white mb-2">Intégration Spotify</h4>
              <p className="text-gray-300">Recherchez et ajoutez des musiques directement depuis Spotify</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">👍</span>
              </div>
              <h4 className="text-xl font-semibold text-white mb-2">Vote en Temps Réel</h4>
              <p className="text-gray-300">Votez avec des emojis pour influencer l'ordre de la playlist</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <QrCode className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-white mb-2">Accès Facile</h4>
              <p className="text-gray-300">Rejoignez rapidement avec un code ou un QR code</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
