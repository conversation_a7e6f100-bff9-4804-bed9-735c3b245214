'use client';

import { useState, useEffect } from 'react';
import { 
  Music, Users, Play, Pause, Ski<PERSON>For<PERSON>, SkipB<PERSON>, Volume2, 
  Settings, Trash2, Crown, Bar<PERSON>hart3, Qr<PERSON><PERSON>, Copy, Check 
} from 'lucide-react';
import Link from 'next/link';

interface Song {
  id: string;
  title: string;
  artist: string;
  albumCover: string;
  duration: number;
  submittedBy: string;
  votes: { [emoji: string]: number };
  isPlaying?: boolean;
}

interface User {
  id: string;
  nickname: string;
  points: number;
  isOnline: boolean;
}

export default function HostDashboard({ params }: { params: { code: string } }) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(75);
  const [currentTime, setCurrentTime] = useState(45);
  const [playlist, setPlaylist] = useState<Song[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [currentSong, setCurrentSong] = useState<Song | null>(null);
  const [copied, setCopied] = useState(false);

  // Mock data
  useEffect(() => {
    setPlaylist([
      {
        id: '1',
        title: 'Blinding Lights',
        artist: 'The Weeknd',
        albumCover: '/api/placeholder/60/60',
        duration: 200,
        submittedBy: 'Alice',
        votes: { '👍': 5, '🔥': 3, '❤️': 2 },
        isPlaying: true
      },
      {
        id: '2',
        title: 'Levitating',
        artist: 'Dua Lipa',
        albumCover: '/api/placeholder/60/60',
        duration: 203,
        submittedBy: 'Bob',
        votes: { '👍': 3, '🔥': 1 }
      },
      {
        id: '3',
        title: 'Good 4 U',
        artist: 'Olivia Rodrigo',
        albumCover: '/api/placeholder/60/60',
        duration: 178,
        submittedBy: 'Charlie',
        votes: { '👍': 2, '❤️': 4 }
      }
    ]);

    setUsers([
      { id: '1', nickname: 'Alice', points: 15, isOnline: true },
      { id: '2', nickname: 'Bob', points: 8, isOnline: true },
      { id: '3', nickname: 'Charlie', points: 12, isOnline: false }
    ]);

    setCurrentSong(playlist[0]);
  }, []);

  const copySessionCode = async () => {
    try {
      await navigator.clipboard.writeText(params.code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Erreur lors de la copie:', err);
    }
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    // TODO: Intégrer avec l'API Spotify
  };

  const handleNext = () => {
    const currentIndex = playlist.findIndex(song => song.isPlaying);
    if (currentIndex < playlist.length - 1) {
      const newPlaylist = playlist.map((song, index) => ({
        ...song,
        isPlaying: index === currentIndex + 1
      }));
      setPlaylist(newPlaylist);
      setCurrentSong(newPlaylist[currentIndex + 1]);
    }
  };

  const handlePrevious = () => {
    const currentIndex = playlist.findIndex(song => song.isPlaying);
    if (currentIndex > 0) {
      const newPlaylist = playlist.map((song, index) => ({
        ...song,
        isPlaying: index === currentIndex - 1
      }));
      setPlaylist(newPlaylist);
      setCurrentSong(newPlaylist[currentIndex - 1]);
    }
  };

  const handleRemoveSong = (songId: string) => {
    setPlaylist(prev => prev.filter(song => song.id !== songId));
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTotalVotes = (votes: { [emoji: string]: number }) => {
    return Object.values(votes).reduce((sum, count) => sum + count, 0);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Crown className="w-8 h-8 text-yellow-400 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-white">Tableau de Bord Hôte</h1>
              <p className="text-gray-300">Session {params.code}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={copySessionCode}
              className="flex items-center space-x-2 px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
            >
              {copied ? <Check className="w-4 h-4 text-white" /> : <Copy className="w-4 h-4 text-white" />}
              <span className="text-white">{params.code}</span>
            </button>
            <Link
              href={`/session/${params.code}`}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              Vue Participant
            </Link>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Controls */}
          <div className="lg:col-span-2 space-y-6">
            {/* Player Controls */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                <Music className="w-6 h-6 mr-2" />
                Contrôles de Lecture
              </h2>
              
              {currentSong && (
                <div className="space-y-4">
                  {/* Current Song Info */}
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-300 rounded-lg flex items-center justify-center">
                      <Music className="w-8 h-8 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white">{currentSong.title}</h3>
                      <p className="text-gray-300">{currentSong.artist}</p>
                      <p className="text-sm text-gray-400">Ajouté par {currentSong.submittedBy}</p>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm text-gray-300">
                      <span>{formatDuration(currentTime)}</span>
                      <span>{formatDuration(currentSong.duration)}</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(currentTime / currentSong.duration) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Control Buttons */}
                  <div className="flex items-center justify-center space-x-4">
                    <button
                      onClick={handlePrevious}
                      className="p-3 bg-white/20 hover:bg-white/30 rounded-full transition-colors"
                    >
                      <SkipBack className="w-6 h-6 text-white" />
                    </button>
                    <button
                      onClick={handlePlayPause}
                      className="p-4 bg-green-500 hover:bg-green-600 rounded-full transition-colors"
                    >
                      {isPlaying ? <Pause className="w-8 h-8 text-white" /> : <Play className="w-8 h-8 text-white" />}
                    </button>
                    <button
                      onClick={handleNext}
                      className="p-3 bg-white/20 hover:bg-white/30 rounded-full transition-colors"
                    >
                      <SkipForward className="w-6 h-6 text-white" />
                    </button>
                  </div>

                  {/* Volume Control */}
                  <div className="flex items-center space-x-4">
                    <Volume2 className="w-5 h-5 text-white" />
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={volume}
                      onChange={(e) => setVolume(Number(e.target.value))}
                      className="flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer"
                    />
                    <span className="text-white text-sm w-12">{volume}%</span>
                  </div>
                </div>
              )}
            </div>

            {/* Playlist Management */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4">Gestion de la Playlist ({playlist.length})</h2>
              <div className="space-y-3">
                {playlist.map((song, index) => (
                  <div key={song.id} className={`flex items-center space-x-4 p-3 rounded-lg ${song.isPlaying ? 'bg-green-500/20' : 'bg-white/5'}`}>
                    <div className="w-8 h-8 flex items-center justify-center text-white font-semibold">
                      {index + 1}
                    </div>
                    <div className="w-12 h-12 bg-gray-300 rounded-lg flex items-center justify-center">
                      <Music className="w-6 h-6 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-white font-medium">{song.title}</h3>
                      <p className="text-gray-300 text-sm">{song.artist} • {song.submittedBy}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1 text-sm text-gray-300">
                        <BarChart3 className="w-4 h-4" />
                        <span>{getTotalVotes(song.votes)} votes</span>
                      </div>
                      <button
                        onClick={() => handleRemoveSong(song.id)}
                        className="p-2 bg-red-500/20 hover:bg-red-500/30 rounded-lg transition-colors"
                      >
                        <Trash2 className="w-4 h-4 text-red-400" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Session Stats */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                <BarChart3 className="w-6 h-6 mr-2" />
                Statistiques
              </h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-300">Participants:</span>
                  <span className="text-white font-medium">{users.filter(u => u.isOnline).length}/{users.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Musiques:</span>
                  <span className="text-white">{playlist.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Votes totaux:</span>
                  <span className="text-white">{playlist.reduce((sum, song) => sum + getTotalVotes(song.votes), 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Durée totale:</span>
                  <span className="text-white">{formatDuration(playlist.reduce((sum, song) => sum + song.duration, 0))}</span>
                </div>
              </div>
            </div>

            {/* Participants */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                <Users className="w-6 h-6 mr-2" />
                Participants ({users.length})
              </h2>
              <div className="space-y-3">
                {users.map(user => (
                  <div key={user.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${user.isOnline ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                      <span className="text-white">{user.nickname}</span>
                    </div>
                    <span className="text-yellow-400 font-medium">{user.points} pts</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Session Settings */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                <Settings className="w-6 h-6 mr-2" />
                Paramètres
              </h2>
              <div className="space-y-4">
                <button className="w-full px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors text-left">
                  Modifier les paramètres
                </button>
                <button className="w-full px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-lg transition-colors text-left">
                  Terminer la session
                </button>
              </div>
            </div>

            {/* QR Code */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                <QrCode className="w-6 h-6 mr-2" />
                Partager
              </h2>
              <div className="text-center">
                <div className="w-32 h-32 bg-white rounded-lg mx-auto flex items-center justify-center mb-4">
                  <QrCode className="w-16 h-16 text-gray-400" />
                </div>
                <p className="text-gray-300 text-sm">QR Code pour rejoindre</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
