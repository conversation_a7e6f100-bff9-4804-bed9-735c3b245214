'use client';

import { useState, useEffect } from 'react';
import { Music, Users, Search, Heart, ThumbsUp, ThumbsDown, Fire, Plus } from 'lucide-react';
import Link from 'next/link';

interface Song {
  id: string;
  title: string;
  artist: string;
  albumCover: string;
  duration: number;
  submittedBy: string;
  votes: { [emoji: string]: number };
  isPlaying?: boolean;
}

interface User {
  id: string;
  nickname: string;
  points: number;
}

export default function SessionPage({ params }: { params: { code: string } }) {
  const [nickname, setNickname] = useState('');
  const [hasJoined, setHasJoined] = useState(false);
  const [playlist, setPlaylist] = useState<Song[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Song[]>([]);
  const [currentSong, setCurrentSong] = useState<Song | null>(null);

  // Mock data pour la démonstration
  useEffect(() => {
    if (hasJoined) {
      setPlaylist([
        {
          id: '1',
          title: 'Blinding Lights',
          artist: 'The Weeknd',
          albumCover: '/api/placeholder/60/60',
          duration: 200,
          submittedBy: 'Alice',
          votes: { '👍': 5, '🔥': 3, '❤️': 2 },
          isPlaying: true
        },
        {
          id: '2',
          title: 'Levitating',
          artist: 'Dua Lipa',
          albumCover: '/api/placeholder/60/60',
          duration: 203,
          submittedBy: 'Bob',
          votes: { '👍': 3, '🔥': 1 }
        },
        {
          id: '3',
          title: 'Good 4 U',
          artist: 'Olivia Rodrigo',
          albumCover: '/api/placeholder/60/60',
          duration: 178,
          submittedBy: 'Charlie',
          votes: { '👍': 2, '❤️': 4 }
        }
      ]);

      setUsers([
        { id: '1', nickname: 'Alice', points: 15 },
        { id: '2', nickname: 'Bob', points: 8 },
        { id: '3', nickname: 'Charlie', points: 12 }
      ]);

      setCurrentSong(playlist[0]);
    }
  }, [hasJoined]);

  const handleJoinSession = () => {
    if (nickname.trim()) {
      setHasJoined(true);
      // TODO: Intégrer avec l'API backend
    }
  };

  const handleVote = (songId: string, emoji: string) => {
    setPlaylist(prev => prev.map(song => {
      if (song.id === songId) {
        const newVotes = { ...song.votes };
        newVotes[emoji] = (newVotes[emoji] || 0) + 1;
        return { ...song, votes: newVotes };
      }
      return song;
    }));
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTotalVotes = (votes: { [emoji: string]: number }) => {
    return Object.values(votes).reduce((sum, count) => sum + count, 0);
  };

  if (!hasJoined) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <Link href="/" className="inline-flex items-center text-white/70 hover:text-white mb-6">
                ← Retour à l'accueil
              </Link>
              <div className="flex items-center justify-center mb-6">
                <Music className="w-12 h-12 text-white mr-4" />
                <h1 className="text-4xl font-bold text-white">Rejoindre</h1>
              </div>
              <p className="text-xl text-gray-300 mb-2">Session: {params.code}</p>
              <p className="text-gray-400">Entrez votre pseudo pour participer</p>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
              <div className="space-y-6">
                <div>
                  <label className="block text-white font-semibold mb-2">
                    Votre pseudo
                  </label>
                  <input
                    type="text"
                    value={nickname}
                    onChange={(e) => setNickname(e.target.value)}
                    placeholder="Ex: Marie, DJ_Cool..."
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    maxLength={20}
                    onKeyPress={(e) => e.key === 'Enter' && handleJoinSession()}
                  />
                </div>

                <button
                  onClick={handleJoinSession}
                  disabled={!nickname.trim()}
                  className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors ${
                    nickname.trim()
                      ? 'bg-blue-500 hover:bg-blue-600 text-white'
                      : 'bg-gray-500 text-gray-300 cursor-not-allowed'
                  }`}
                >
                  <Users className="w-5 h-5 mr-2 inline" />
                  Rejoindre la Session
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Music className="w-8 h-8 text-white mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-white">Session {params.code}</h1>
              <p className="text-gray-300">Connecté en tant que {nickname}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center text-white">
              <Users className="w-5 h-5 mr-2" />
              <span>{users.length}</span>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Playlist */}
          <div className="lg:col-span-2 space-y-6">
            {/* Current Song */}
            {currentSong && (
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
                <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                  <Music className="w-6 h-6 mr-2" />
                  En cours de lecture
                </h2>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gray-300 rounded-lg flex items-center justify-center">
                    <Music className="w-8 h-8 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white">{currentSong.title}</h3>
                    <p className="text-gray-300">{currentSong.artist}</p>
                    <p className="text-sm text-gray-400">Ajouté par {currentSong.submittedBy}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-medium">{formatDuration(currentSong.duration)}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Add Song */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                <Plus className="w-6 h-6 mr-2" />
                Ajouter une musique
              </h2>
              <div className="flex space-x-4">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Rechercher une musique..."
                  className="flex-1 px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button className="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
                  <Search className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Playlist */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4">Playlist ({playlist.length})</h2>
              <div className="space-y-3">
                {playlist.map((song, index) => (
                  <div key={song.id} className={`flex items-center space-x-4 p-3 rounded-lg ${song.isPlaying ? 'bg-green-500/20' : 'bg-white/5'}`}>
                    <div className="w-8 h-8 flex items-center justify-center text-white font-semibold">
                      {index + 1}
                    </div>
                    <div className="w-12 h-12 bg-gray-300 rounded-lg flex items-center justify-center">
                      <Music className="w-6 h-6 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-white font-medium">{song.title}</h3>
                      <p className="text-gray-300 text-sm">{song.artist} • {song.submittedBy}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {['👍', '🔥', '❤️', '👎'].map(emoji => (
                        <button
                          key={emoji}
                          onClick={() => handleVote(song.id, emoji)}
                          className="flex items-center space-x-1 px-2 py-1 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
                        >
                          <span>{emoji}</span>
                          <span className="text-white text-sm">{song.votes[emoji] || 0}</span>
                        </button>
                      ))}
                    </div>
                    <div className="text-white text-sm">
                      {formatDuration(song.duration)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Participants */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                <Users className="w-6 h-6 mr-2" />
                Participants ({users.length})
              </h2>
              <div className="space-y-3">
                {users.map(user => (
                  <div key={user.id} className="flex items-center justify-between">
                    <span className="text-white">{user.nickname}</span>
                    <span className="text-yellow-400 font-medium">{user.points} pts</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Session Info */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4">Infos Session</h2>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">Code:</span>
                  <span className="text-white font-medium">{params.code}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Musiques:</span>
                  <span className="text-white">{playlist.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Votes totaux:</span>
                  <span className="text-white">{playlist.reduce((sum, song) => sum + getTotalVotes(song.votes), 0)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
