import { Music, Users, Search, Plus, Wifi, AlertCircle } from 'lucide-react';
import { clsx } from 'clsx';

interface EmptyStateProps {
  variant: 'playlist' | 'users' | 'search' | 'connection' | 'error' | 'custom';
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary';
  };
  icon?: React.ReactNode;
  className?: string;
}

export default function EmptyState({
  variant,
  title,
  description,
  action,
  icon,
  className
}: EmptyStateProps) {
  const getDefaultIcon = () => {
    switch (variant) {
      case 'playlist':
        return <Music className="w-16 h-16 text-white/40" />;
      case 'users':
        return <Users className="w-16 h-16 text-white/40" />;
      case 'search':
        return <Search className="w-16 h-16 text-white/40" />;
      case 'connection':
        return <Wifi className="w-16 h-16 text-white/40" />;
      case 'error':
        return <AlertCircle className="w-16 h-16 text-red-400" />;
      default:
        return <Music className="w-16 h-16 text-white/40" />;
    }
  };

  const getActionVariantClasses = (actionVariant: 'primary' | 'secondary' = 'primary') => {
    switch (actionVariant) {
      case 'primary':
        return 'bg-blue-500 hover:bg-blue-600 text-white';
      case 'secondary':
        return 'bg-white/20 hover:bg-white/30 text-white border border-white/30';
      default:
        return 'bg-blue-500 hover:bg-blue-600 text-white';
    }
  };

  return (
    <div className={clsx(
      'flex flex-col items-center justify-center text-center p-8 space-y-4',
      className
    )}>
      {/* Icon */}
      <div className="mb-4">
        {icon || getDefaultIcon()}
      </div>

      {/* Title */}
      <h3 className="text-xl font-semibold text-white">
        {title}
      </h3>

      {/* Description */}
      {description && (
        <p className="text-gray-300 max-w-md">
          {description}
        </p>
      )}

      {/* Action Button */}
      {action && (
        <button
          onClick={action.onClick}
          className={clsx(
            'mt-6 px-6 py-3 rounded-lg font-semibold transition-colors',
            getActionVariantClasses(action.variant)
          )}
        >
          {action.label}
        </button>
      )}
    </div>
  );
}

// États vides prédéfinis
export function EmptyPlaylist({ onAddSong }: { onAddSong?: () => void }) {
  return (
    <EmptyState
      variant="playlist"
      title="Aucune musique dans la playlist"
      description="Commencez par ajouter quelques musiques pour faire démarrer la fête !"
      action={onAddSong ? {
        label: "Ajouter une musique",
        onClick: onAddSong,
        variant: "primary"
      } : undefined}
      icon={
        <div className="relative">
          <Music className="w-16 h-16 text-white/40" />
          <Plus className="w-6 h-6 text-green-400 absolute -bottom-1 -right-1 bg-white/20 rounded-full p-1" />
        </div>
      }
    />
  );
}

export function EmptyUsers() {
  return (
    <EmptyState
      variant="users"
      title="Aucun participant"
      description="Partagez le code de session pour inviter vos amis à rejoindre la playlist !"
    />
  );
}

export function EmptySearchResults({ query, onClearSearch }: { 
  query?: string; 
  onClearSearch?: () => void; 
}) {
  return (
    <EmptyState
      variant="search"
      title="Aucun résultat trouvé"
      description={query ? `Aucune musique trouvée pour "${query}"` : "Essayez avec d'autres mots-clés"}
      action={onClearSearch ? {
        label: "Effacer la recherche",
        onClick: onClearSearch,
        variant: "secondary"
      } : undefined}
    />
  );
}

export function ConnectionError({ onRetry }: { onRetry?: () => void }) {
  return (
    <EmptyState
      variant="connection"
      title="Problème de connexion"
      description="Impossible de se connecter au serveur. Vérifiez votre connexion internet."
      action={onRetry ? {
        label: "Réessayer",
        onClick: onRetry,
        variant: "primary"
      } : undefined}
      icon={<Wifi className="w-16 h-16 text-red-400" />}
    />
  );
}

export function SessionNotFound({ onGoHome }: { onGoHome?: () => void }) {
  return (
    <EmptyState
      variant="error"
      title="Session introuvable"
      description="Cette session n'existe pas ou a expiré. Vérifiez le code de session."
      action={onGoHome ? {
        label: "Retour à l'accueil",
        onClick: onGoHome,
        variant: "primary"
      } : undefined}
    />
  );
}

export function SpotifyNotConnected({ onConnect }: { onConnect?: () => void }) {
  return (
    <EmptyState
      variant="custom"
      title="Spotify non connecté"
      description="Connectez votre compte Spotify pour rechercher et ajouter des musiques."
      action={onConnect ? {
        label: "Connecter Spotify",
        onClick: onConnect,
        variant: "primary"
      } : undefined}
      icon={
        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
          <span className="text-white font-bold text-2xl">♪</span>
        </div>
      }
    />
  );
}

// Composant de carte vide
export function EmptyCard({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div className={clsx(
      'bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20',
      className
    )}>
      {children}
    </div>
  );
}
