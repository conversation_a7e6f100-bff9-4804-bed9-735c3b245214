import { Music, Loader2 } from 'lucide-react';
import { clsx } from 'clsx';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'music' | 'dots' | 'pulse';
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

export default function Loading({ 
  size = 'md', 
  variant = 'spinner', 
  text, 
  className,
  fullScreen = false 
}: LoadingProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'md':
        return 'w-6 h-6';
      case 'lg':
        return 'w-8 h-8';
      case 'xl':
        return 'w-12 h-12';
      default:
        return 'w-6 h-6';
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'sm':
        return 'text-sm';
      case 'md':
        return 'text-base';
      case 'lg':
        return 'text-lg';
      case 'xl':
        return 'text-xl';
      default:
        return 'text-base';
    }
  };

  const renderSpinner = () => (
    <Loader2 className={clsx('animate-spin text-white', getSizeClasses())} />
  );

  const renderMusic = () => (
    <Music className={clsx('animate-pulse text-white', getSizeClasses())} />
  );

  const renderDots = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={clsx(
            'bg-white rounded-full animate-bounce',
            size === 'sm' ? 'w-1 h-1' : 
            size === 'md' ? 'w-2 h-2' : 
            size === 'lg' ? 'w-3 h-3' : 'w-4 h-4'
          )}
          style={{
            animationDelay: `${i * 0.1}s`,
            animationDuration: '0.6s'
          }}
        />
      ))}
    </div>
  );

  const renderPulse = () => (
    <div className="flex space-x-1">
      {[0, 1, 2, 3, 4].map((i) => (
        <div
          key={i}
          className={clsx(
            'bg-white rounded-sm animate-pulse',
            size === 'sm' ? 'w-1 h-3' : 
            size === 'md' ? 'w-1 h-4' : 
            size === 'lg' ? 'w-2 h-6' : 'w-2 h-8'
          )}
          style={{
            animationDelay: `${i * 0.1}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );

  const renderLoader = () => {
    switch (variant) {
      case 'spinner':
        return renderSpinner();
      case 'music':
        return renderMusic();
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      default:
        return renderSpinner();
    }
  };

  const content = (
    <div className={clsx(
      'flex flex-col items-center justify-center space-y-3',
      className
    )}>
      {renderLoader()}
      {text && (
        <p className={clsx('text-white/80 font-medium', getTextSize())}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center z-50">
        {content}
      </div>
    );
  }

  return content;
}

// Composant de chargement pour les pages
export function PageLoading({ text = "Chargement..." }: { text?: string }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
      <Loading variant="music" size="xl" text={text} />
    </div>
  );
}

// Composant de chargement pour les cartes/sections
export function CardLoading({ text }: { text?: string }) {
  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 flex items-center justify-center">
      <Loading variant="spinner" size="lg" text={text} />
    </div>
  );
}

// Composant de chargement inline
export function InlineLoading({ text }: { text?: string }) {
  return (
    <div className="flex items-center space-x-2">
      <Loading variant="spinner" size="sm" />
      {text && <span className="text-white/80 text-sm">{text}</span>}
    </div>
  );
}

// Composant de chargement pour les boutons
export function ButtonLoading({ size = 'sm' }: { size?: 'sm' | 'md' }) {
  return <Loading variant="spinner" size={size} className="text-current" />;
}
