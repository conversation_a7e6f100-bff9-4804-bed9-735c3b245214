'use client';

import { useState } from 'react';
import { Search, Plus, Music, Clock } from 'lucide-react';

interface SearchResult {
  id: string;
  title: string;
  artist: string;
  album: string;
  duration: number;
  albumCover?: string;
  spotifyId: string;
}

interface MusicSearchProps {
  onAddSong: (song: SearchResult) => void;
  disabled?: boolean;
}

export default function MusicSearch({ onAddSong, disabled = false }: MusicSearchProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);

  // Mock search results pour la démonstration
  const mockResults: SearchResult[] = [
    {
      id: '1',
      title: 'As It Was',
      artist: 'Harry Styles',
      album: 'Harry\'s House',
      duration: 167,
      spotifyId: 'spotify:track:4Dvkj6JhhA12EX05fT7y2e'
    },
    {
      id: '2',
      title: 'Heat Waves',
      artist: 'Glass Animals',
      album: 'Dreamland',
      duration: 238,
      spotifyId: 'spotify:track:02MWAaffLxlfxAUY7c5dvx'
    },
    {
      id: '3',
      title: 'Stay',
      artist: 'The Kid LAROI, Justin Bieber',
      album: 'F*CK LOVE 3: OVER YOU',
      duration: 141,
      spotifyId: 'spotify:track:5PjdY0CKGZdEuoNab3yDmX'
    }
  ];

  const handleSearch = async () => {
    if (!query.trim()) return;

    setIsSearching(true);
    setShowResults(true);

    // Simuler une recherche API
    setTimeout(() => {
      const filteredResults = mockResults.filter(song => 
        song.title.toLowerCase().includes(query.toLowerCase()) ||
        song.artist.toLowerCase().includes(query.toLowerCase())
      );
      setResults(filteredResults);
      setIsSearching(false);
    }, 1000);

    // TODO: Intégrer avec l'API Spotify
    // const response = await fetch(`/api/spotify/search?q=${encodeURIComponent(query)}`);
    // const data = await response.json();
    // setResults(data.tracks);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAddSong = (song: SearchResult) => {
    onAddSong(song);
    setShowResults(false);
    setQuery('');
    setResults([]);
  };

  return (
    <div className="relative">
      {/* Search Input */}
      <div className="flex space-x-4">
        <div className="flex-1 relative">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Rechercher une musique, un artiste..."
            disabled={disabled}
            className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
          />
          {isSearching && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            </div>
          )}
        </div>
        <button
          onClick={handleSearch}
          disabled={!query.trim() || isSearching || disabled}
          className="px-6 py-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-500 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          <Search className="w-5 h-5" />
        </button>
      </div>

      {/* Search Results */}
      {showResults && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg shadow-xl z-50 max-h-96 overflow-y-auto">
          {isSearching ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-white">Recherche en cours...</p>
            </div>
          ) : results.length > 0 ? (
            <div className="p-2">
              <div className="flex items-center justify-between p-3 border-b border-white/20">
                <span className="text-white font-medium">Résultats pour "{query}"</span>
                <button
                  onClick={() => setShowResults(false)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              </div>
              <div className="space-y-1 mt-2">
                {results.map((song) => (
                  <div
                    key={song.id}
                    className="flex items-center space-x-3 p-3 hover:bg-white/10 rounded-lg transition-colors group"
                  >
                    {/* Album Cover */}
                    <div className="w-12 h-12 bg-gray-300 rounded-lg flex items-center justify-center">
                      {song.albumCover ? (
                        <img src={song.albumCover} alt={`${song.title} cover`} className="w-full h-full object-cover rounded-lg" />
                      ) : (
                        <Music className="w-6 h-6 text-gray-600" />
                      )}
                    </div>

                    {/* Song Info */}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-white font-medium truncate">{song.title}</h4>
                      <p className="text-gray-300 text-sm truncate">{song.artist}</p>
                      <p className="text-gray-400 text-xs truncate">{song.album}</p>
                    </div>

                    {/* Duration */}
                    <div className="flex items-center text-gray-400 text-sm">
                      <Clock className="w-4 h-4 mr-1" />
                      {formatDuration(song.duration)}
                    </div>

                    {/* Add Button */}
                    <button
                      onClick={() => handleAddSong(song)}
                      className="p-2 bg-green-500/20 hover:bg-green-500/30 text-green-400 rounded-lg transition-colors opacity-0 group-hover:opacity-100"
                      title="Ajouter à la playlist"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="p-6 text-center">
              <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-white mb-2">Aucun résultat trouvé</p>
              <p className="text-gray-400 text-sm">Essayez avec d'autres mots-clés</p>
              <button
                onClick={() => setShowResults(false)}
                className="mt-4 px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
              >
                Fermer
              </button>
            </div>
          )}
        </div>
      )}

      {/* Overlay to close results */}
      {showResults && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowResults(false)}
        ></div>
      )}
    </div>
  );
}
