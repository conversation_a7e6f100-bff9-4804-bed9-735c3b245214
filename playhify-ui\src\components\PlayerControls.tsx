'use client';

import { useState, useEffect } from 'react';
import { Play, Pause, SkipForward, SkipBack, Volume2, VolumeX, Shuffle, Repeat } from 'lucide-react';

interface Song {
  id: string;
  title: string;
  artist: string;
  albumCover?: string;
  duration: number;
}

interface PlayerControlsProps {
  currentSong: Song | null;
  isPlaying: boolean;
  volume: number;
  currentTime: number;
  onPlayPause: () => void;
  onNext: () => void;
  onPrevious: () => void;
  onVolumeChange: (volume: number) => void;
  onSeek?: (time: number) => void;
  canSkipPrevious?: boolean;
  canSkipNext?: boolean;
}

export default function PlayerControls({
  currentSong,
  isPlaying,
  volume,
  currentTime,
  onPlayPause,
  onNext,
  onPrevious,
  onVolumeChange,
  onSeek,
  canSkipPrevious = true,
  canSkipNext = true
}: PlayerControlsProps) {
  const [isMuted, setIsMuted] = useState(false);
  const [previousVolume, setPreviousVolume] = useState(volume);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleMute = () => {
    if (isMuted) {
      onVolumeChange(previousVolume);
      setIsMuted(false);
    } else {
      setPreviousVolume(volume);
      onVolumeChange(0);
      setIsMuted(true);
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    onVolumeChange(newVolume);
    if (newVolume === 0) {
      setIsMuted(true);
    } else if (isMuted) {
      setIsMuted(false);
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!currentSong || !onSeek) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const width = rect.width;
    const percentage = clickX / width;
    const newTime = Math.floor(percentage * currentSong.duration);
    
    onSeek(newTime);
  };

  if (!currentSong) {
    return (
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
        <div className="text-center text-gray-400">
          <Play className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>Aucune musique sélectionnée</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
      <div className="space-y-6">
        {/* Current Song Info */}
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-300 rounded-lg flex items-center justify-center overflow-hidden">
            {currentSong.albumCover ? (
              <img 
                src={currentSong.albumCover} 
                alt={`${currentSong.title} cover`} 
                className="w-full h-full object-cover" 
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-purple-400 to-blue-500 flex items-center justify-center">
                <span className="text-white font-bold text-lg">♪</span>
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-white truncate">{currentSong.title}</h3>
            <p className="text-gray-300 truncate">{currentSong.artist}</p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-300">
            <span>{formatDuration(currentTime)}</span>
            <span>{formatDuration(currentSong.duration)}</span>
          </div>
          <div 
            className="w-full bg-white/20 rounded-full h-2 cursor-pointer group"
            onClick={handleProgressClick}
          >
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-300 group-hover:bg-green-400 relative"
              style={{ width: `${(currentTime / currentSong.duration) * 100}%` }}
            >
              <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"></div>
            </div>
          </div>
        </div>

        {/* Main Controls */}
        <div className="flex items-center justify-center space-x-6">
          <button
            onClick={onPrevious}
            disabled={!canSkipPrevious}
            className="p-3 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed rounded-full transition-colors"
            title="Précédent"
          >
            <SkipBack className="w-6 h-6 text-white" />
          </button>
          
          <button
            onClick={onPlayPause}
            className="p-4 bg-green-500 hover:bg-green-600 rounded-full transition-colors shadow-lg hover:shadow-xl transform hover:scale-105"
            title={isPlaying ? 'Pause' : 'Lecture'}
          >
            {isPlaying ? (
              <Pause className="w-8 h-8 text-white" />
            ) : (
              <Play className="w-8 h-8 text-white ml-1" />
            )}
          </button>
          
          <button
            onClick={onNext}
            disabled={!canSkipNext}
            className="p-3 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed rounded-full transition-colors"
            title="Suivant"
          >
            <SkipForward className="w-6 h-6 text-white" />
          </button>
        </div>

        {/* Volume Control */}
        <div className="flex items-center space-x-4">
          <button
            onClick={handleMute}
            className="p-2 hover:bg-white/20 rounded-lg transition-colors"
            title={isMuted ? 'Activer le son' : 'Couper le son'}
          >
            {isMuted || volume === 0 ? (
              <VolumeX className="w-5 h-5 text-white" />
            ) : (
              <Volume2 className="w-5 h-5 text-white" />
            )}
          </button>
          
          <div className="flex-1 flex items-center space-x-3">
            <input
              type="range"
              min="0"
              max="100"
              value={isMuted ? 0 : volume}
              onChange={(e) => handleVolumeChange(Number(e.target.value))}
              className="flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, #10b981 0%, #10b981 ${isMuted ? 0 : volume}%, rgba(255,255,255,0.2) ${isMuted ? 0 : volume}%, rgba(255,255,255,0.2) 100%)`
              }}
            />
            <span className="text-white text-sm w-12 text-right">
              {isMuted ? 0 : volume}%
            </span>
          </div>
        </div>

        {/* Additional Controls (Optional) */}
        <div className="flex items-center justify-center space-x-4 pt-2 border-t border-white/20">
          <button
            className="p-2 hover:bg-white/20 rounded-lg transition-colors opacity-50 cursor-not-allowed"
            title="Lecture aléatoire (bientôt disponible)"
            disabled
          >
            <Shuffle className="w-4 h-4 text-white" />
          </button>
          
          <button
            className="p-2 hover:bg-white/20 rounded-lg transition-colors opacity-50 cursor-not-allowed"
            title="Répéter (bientôt disponible)"
            disabled
          >
            <Repeat className="w-4 h-4 text-white" />
          </button>
        </div>
      </div>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #ffffff;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .slider::-moz-range-thumb {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #ffffff;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </div>
  );
}
