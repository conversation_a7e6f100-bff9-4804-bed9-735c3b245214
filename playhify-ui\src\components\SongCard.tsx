import { Music, Trash2, Bar<PERSON>hart3 } from 'lucide-react';

interface Song {
  id: string;
  title: string;
  artist: string;
  albumCover: string;
  duration: number;
  submittedBy: string;
  votes: { [emoji: string]: number };
  isPlaying?: boolean;
}

interface SongCardProps {
  song: Song;
  index: number;
  onVote?: (songId: string, emoji: string) => void;
  onRemove?: (songId: string) => void;
  isHost?: boolean;
  showVoting?: boolean;
}

export default function SongCard({ 
  song, 
  index, 
  onVote, 
  onRemove, 
  isHost = false, 
  showVoting = true 
}: SongCardProps) {
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTotalVotes = (votes: { [emoji: string]: number }) => {
    return Object.values(votes).reduce((sum, count) => sum + count, 0);
  };

  return (
    <div className={`flex items-center space-x-4 p-3 rounded-lg transition-all ${
      song.isPlaying ? 'bg-green-500/20 border border-green-500/30' : 'bg-white/5 hover:bg-white/10'
    }`}>
      {/* Position */}
      <div className="w-8 h-8 flex items-center justify-center text-white font-semibold">
        {song.isPlaying ? (
          <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
        ) : (
          index + 1
        )}
      </div>

      {/* Album Cover */}
      <div className="w-12 h-12 bg-gray-300 rounded-lg flex items-center justify-center overflow-hidden">
        {song.albumCover ? (
          <img src={song.albumCover} alt={`${song.title} cover`} className="w-full h-full object-cover" />
        ) : (
          <Music className="w-6 h-6 text-gray-600" />
        )}
      </div>

      {/* Song Info */}
      <div className="flex-1 min-w-0">
        <h3 className="text-white font-medium truncate">{song.title}</h3>
        <p className="text-gray-300 text-sm truncate">{song.artist}</p>
        <p className="text-gray-400 text-xs">Ajouté par {song.submittedBy}</p>
      </div>

      {/* Voting Section */}
      {showVoting && onVote && (
        <div className="flex items-center space-x-2">
          {['👍', '🔥', '❤️', '👎'].map(emoji => (
            <button
              key={emoji}
              onClick={() => onVote(song.id, emoji)}
              className="flex items-center space-x-1 px-2 py-1 bg-white/10 hover:bg-white/20 rounded-lg transition-colors group"
              title={`Voter ${emoji}`}
            >
              <span className="group-hover:scale-110 transition-transform">{emoji}</span>
              <span className="text-white text-sm">{song.votes[emoji] || 0}</span>
            </button>
          ))}
        </div>
      )}

      {/* Host Controls */}
      {isHost && (
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 text-sm text-gray-300">
            <BarChart3 className="w-4 h-4" />
            <span>{getTotalVotes(song.votes)} votes</span>
          </div>
          {onRemove && (
            <button
              onClick={() => onRemove(song.id)}
              className="p-2 bg-red-500/20 hover:bg-red-500/30 rounded-lg transition-colors group"
              title="Supprimer cette musique"
            >
              <Trash2 className="w-4 h-4 text-red-400 group-hover:text-red-300" />
            </button>
          )}
        </div>
      )}

      {/* Duration */}
      <div className="text-white text-sm font-medium">
        {formatDuration(song.duration)}
      </div>
    </div>
  );
}
