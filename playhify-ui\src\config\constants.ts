// Configuration et constantes pour PlayHify

// Configuration de l'application
export const APP_CONFIG = {
  name: 'PlayHify',
  version: '1.0.0',
  description: 'Application de playlist collaborative en temps réel',
  author: 'PlayHify Team',
  website: 'https://playhify.com',
} as const;

// Configuration des sessions
export const SESSION_CONFIG = {
  codeLength: 6,
  maxSessionName: 50,
  maxNickname: 20,
  maxUsers: 50,
  defaultMaxSongsPerUser: 5,
  sessionTimeout: 24 * 60 * 60 * 1000, // 24 heures en millisecondes
  inactivityTimeout: 30 * 60 * 1000, // 30 minutes
} as const;

// Configuration des musiques
export const MUSIC_CONFIG = {
  maxPlaylistSize: 100,
  maxSearchResults: 20,
  searchDebounceMs: 500,
  defaultVolume: 75,
  seekStepSeconds: 10,
} as const;

// Configuration du vote
export const VOTING_CONFIG = {
  availableEmojis: ['👍', '👎', '🔥', '❤️', '😍', '🎵', '💯'] as const,
  defaultVoteWeight: 1,
  maxVotesPerSong: 1, // par utilisateur
  voteTimeout: 5000, // délai avant de pouvoir voter à nouveau (ms)
} as const;

// Configuration de la gamification
export const GAMIFICATION_CONFIG = {
  points: {
    songAdded: 5,
    songVotedPositive: 2,
    songPlayed: 10,
    sessionCreated: 20,
    firstToJoin: 5,
  },
  badges: {
    dj: { threshold: 10, description: 'Ajouter 10 musiques' },
    popular: { threshold: 50, description: 'Recevoir 50 votes positifs' },
    host: { threshold: 1, description: 'Créer une session' },
    social: { threshold: 5, description: 'Rejoindre 5 sessions' },
    earlyBird: { threshold: 1, description: 'Premier à rejoindre une session' },
  },
} as const;

// Configuration WebSocket
export const WEBSOCKET_CONFIG = {
  reconnectAttempts: 5,
  reconnectDelay: 1000,
  heartbeatInterval: 30000,
  connectionTimeout: 10000,
} as const;

// Configuration Spotify
export const SPOTIFY_CONFIG = {
  clientId: process.env.NEXT_PUBLIC_SPOTIFY_CLIENT_ID || '',
  redirectUri: process.env.NEXT_PUBLIC_SPOTIFY_REDIRECT_URI || 'http://localhost:3000/auth/spotify/callback',
  scopes: [
    'streaming',
    'user-read-email',
    'user-read-private',
    'user-read-playback-state',
    'user-modify-playback-state',
    'playlist-modify-public',
    'playlist-modify-private',
  ],
  apiBaseUrl: 'https://api.spotify.com/v1',
} as const;

// Configuration Supabase
export const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  realtimeChannel: 'playhify-sessions',
} as const;

// Messages d'erreur
export const ERROR_MESSAGES = {
  // Erreurs de session
  sessionNotFound: 'Session introuvable',
  sessionExpired: 'Session expirée',
  sessionFull: 'Session complète',
  invalidSessionCode: 'Code de session invalide',
  
  // Erreurs d\'utilisateur
  nicknameRequired: 'Pseudo requis',
  nicknameTooLong: `Pseudo trop long (max ${SESSION_CONFIG.maxNickname} caractères)`,
  nicknameAlreadyTaken: 'Ce pseudo est déjà pris',
  userNotFound: 'Utilisateur introuvable',
  
  // Erreurs de musique
  songNotFound: 'Musique introuvable',
  playlistFull: `Playlist complète (max ${MUSIC_CONFIG.maxPlaylistSize} musiques)`,
  songAlreadyInPlaylist: 'Cette musique est déjà dans la playlist',
  maxSongsReached: 'Limite de musiques atteinte pour cet utilisateur',
  
  // Erreurs Spotify
  spotifyNotConnected: 'Spotify non connecté',
  spotifyAuthRequired: 'Authentification Spotify requise',
  spotifyApiError: 'Erreur de l\'API Spotify',
  
  // Erreurs réseau
  networkError: 'Erreur de connexion',
  serverError: 'Erreur serveur',
  timeout: 'Délai d\'attente dépassé',
  
  // Erreurs générales
  unknownError: 'Erreur inconnue',
  permissionDenied: 'Permission refusée',
  invalidInput: 'Données invalides',
} as const;

// Messages de succès
export const SUCCESS_MESSAGES = {
  sessionCreated: 'Session créée avec succès',
  sessionJoined: 'Session rejointe avec succès',
  songAdded: 'Musique ajoutée à la playlist',
  voteSubmitted: 'Vote enregistré',
  settingsUpdated: 'Paramètres mis à jour',
  spotifyConnected: 'Spotify connecté avec succès',
} as const;

// Configuration de l'interface utilisateur
export const UI_CONFIG = {
  animations: {
    duration: 300,
    easing: 'ease-in-out',
  },
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  colors: {
    primary: '#10b981', // green-500
    secondary: '#3b82f6', // blue-500
    accent: '#f59e0b', // amber-500
    danger: '#ef4444', // red-500
    warning: '#f59e0b', // amber-500
    success: '#10b981', // green-500
    info: '#3b82f6', // blue-500
  },
  toast: {
    duration: 4000,
    position: 'top-right' as const,
  },
} as const;

// Configuration des notifications
export const NOTIFICATION_CONFIG = {
  types: {
    songAdded: {
      title: 'Nouvelle musique',
      icon: '🎵',
      duration: 3000,
    },
    userJoined: {
      title: 'Nouvel utilisateur',
      icon: '👋',
      duration: 2000,
    },
    voteReceived: {
      title: 'Nouveau vote',
      icon: '👍',
      duration: 2000,
    },
    playlistUpdated: {
      title: 'Playlist mise à jour',
      icon: '🔄',
      duration: 2000,
    },
  },
  maxNotifications: 5,
  stackSpacing: 10,
} as const;

// Configuration du cache
export const CACHE_CONFIG = {
  keys: {
    userPreferences: 'playhify_user_preferences',
    recentSessions: 'playhify_recent_sessions',
    spotifyToken: 'playhify_spotify_token',
  },
  ttl: {
    userPreferences: 7 * 24 * 60 * 60 * 1000, // 7 jours
    recentSessions: 24 * 60 * 60 * 1000, // 24 heures
    spotifyToken: 60 * 60 * 1000, // 1 heure
  },
} as const;

// URLs de l'API
export const API_ENDPOINTS = {
  base: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
  sessions: '/sessions',
  users: '/users',
  songs: '/songs',
  votes: '/votes',
  spotify: '/spotify',
  auth: '/auth',
} as const;

// Configuration des logs
export const LOG_CONFIG = {
  level: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
  enableConsole: process.env.NODE_ENV !== 'production',
  enableRemote: process.env.NODE_ENV === 'production',
} as const;

// Regex patterns
export const PATTERNS = {
  sessionCode: /^[A-Z0-9]{6}$/,
  nickname: /^[a-zA-Z0-9_\-\s]{1,20}$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
} as const;

// Types pour TypeScript
export type VoteEmoji = typeof VOTING_CONFIG.availableEmojis[number];
export type NotificationType = keyof typeof NOTIFICATION_CONFIG.types;
export type CacheKey = keyof typeof CACHE_CONFIG.keys;
