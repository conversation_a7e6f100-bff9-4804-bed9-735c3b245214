'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User, Session, Song } from '@/types';
import { useToast, ToastContainer } from '@/components/Toast';

// Types pour le contexte
interface AppState {
  // Utilisateur actuel
  currentUser: User | null;
  
  // Session actuelle
  currentSession: Session | null;
  
  // État de l'interface
  isLoading: boolean;
  error: string | null;
  
  // Préférences utilisateur
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    notifications: boolean;
    volume: number;
  };
}

type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_SESSION'; payload: Session | null }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<AppState['preferences']> }
  | { type: 'CLEAR_ALL' };

interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  
  // Actions de convenance
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updatePreferences: (preferences: Partial<AppState['preferences']>) => void;
  clearAll: () => void;
  
  // Toast functions
  toast: ReturnType<typeof useToast>;
}

// État initial
const initialState: AppState = {
  currentUser: null,
  currentSession: null,
  isLoading: false,
  error: null,
  preferences: {
    theme: 'dark',
    notifications: true,
    volume: 75,
  },
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        currentUser: action.payload,
      };
    
    case 'SET_SESSION':
      return {
        ...state,
        currentSession: action.payload,
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: {
          ...state.preferences,
          ...action.payload,
        },
      };
    
    case 'CLEAR_ALL':
      return {
        ...initialState,
        preferences: state.preferences, // Garder les préférences
      };
    
    default:
      return state;
  }
}

// Contexte
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider
interface AppProviderProps {
  children: React.ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const toast = useToast();

  // Charger les préférences depuis le localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedPreferences = localStorage.getItem('playhify_preferences');
      if (savedPreferences) {
        try {
          const preferences = JSON.parse(savedPreferences);
          dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
        } catch (error) {
          console.error('Erreur lors du chargement des préférences:', error);
        }
      }
    }
  }, []);

  // Sauvegarder les préférences dans le localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('playhify_preferences', JSON.stringify(state.preferences));
    }
  }, [state.preferences]);

  // Actions de convenance
  const setUser = (user: User | null) => {
    dispatch({ type: 'SET_USER', payload: user });
  };

  const setSession = (session: Session | null) => {
    dispatch({ type: 'SET_SESSION', payload: session });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const setError = (error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
    
    // Afficher une notification d'erreur
    if (error) {
      toast.error('Erreur', error);
    }
  };

  const updatePreferences = (preferences: Partial<AppState['preferences']>) => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
  };

  const clearAll = () => {
    dispatch({ type: 'CLEAR_ALL' });
  };

  // Gestion du thème
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const root = window.document.documentElement;
      
      if (state.preferences.theme === 'dark') {
        root.classList.add('dark');
      } else if (state.preferences.theme === 'light') {
        root.classList.remove('dark');
      } else {
        // Auto: utiliser la préférence système
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        if (mediaQuery.matches) {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      }
    }
  }, [state.preferences.theme]);

  const contextValue: AppContextType = {
    state,
    dispatch,
    setUser,
    setSession,
    setLoading,
    setError,
    updatePreferences,
    clearAll,
    toast,
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
      <ToastContainer 
        toasts={toast.toasts} 
        onClose={toast.removeToast}
        position="top-right"
      />
    </AppContext.Provider>
  );
}

// Hook pour utiliser le contexte
export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

// Hook pour les préférences utilisateur
export function usePreferences() {
  const { state, updatePreferences } = useApp();
  
  return {
    preferences: state.preferences,
    updatePreferences,
    setTheme: (theme: 'light' | 'dark' | 'auto') => 
      updatePreferences({ theme }),
    setNotifications: (notifications: boolean) => 
      updatePreferences({ notifications }),
    setVolume: (volume: number) => 
      updatePreferences({ volume }),
  };
}

// Hook pour l'utilisateur actuel
export function useCurrentUser() {
  const { state, setUser } = useApp();
  
  return {
    user: state.currentUser,
    setUser,
    isLoggedIn: !!state.currentUser,
    isHost: state.currentUser?.isHost || false,
  };
}

// Hook pour la session actuelle
export function useCurrentSession() {
  const { state, setSession } = useApp();
  
  return {
    session: state.currentSession,
    setSession,
    isInSession: !!state.currentSession,
    sessionCode: state.currentSession?.code,
  };
}

// Hook pour l'état de chargement et les erreurs
export function useAppState() {
  const { state, setLoading, setError } = useApp();
  
  return {
    isLoading: state.isLoading,
    error: state.error,
    setLoading,
    setError,
    clearError: () => setError(null),
  };
}
