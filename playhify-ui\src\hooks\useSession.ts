'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { Song, User, SessionState, WebSocketEvent } from '@/types';
import { WEBSOCKET_CONFIG, API_ENDPOINTS } from '@/config/constants';

interface UseSessionProps {
  sessionCode: string;
  userId?: string;
  isHost?: boolean;
}

interface SessionData {
  playlist: Song[];
  users: User[];
  currentSong: Song | null;
  sessionState: SessionState | null;
  isConnected: boolean;
  error: string | null;
}

export function useSession({ sessionCode, userId, isHost = false }: UseSessionProps) {
  const [sessionData, setSessionData] = useState<SessionData>({
    playlist: [],
    users: [],
    currentSong: null,
    sessionState: null,
    isConnected: false,
    error: null,
  });

  const socketRef = useRef<Socket | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Connexion WebSocket
  const connect = useCallback(() => {
    if (socketRef.current?.connected) return;

    try {
      const socket = io(API_ENDPOINTS.base.replace('/api', ''), {
        transports: ['websocket'],
        timeout: WEBSOCKET_CONFIG.connectionTimeout,
        forceNew: true,
      });

      socketRef.current = socket;

      // Événements de connexion
      socket.on('connect', () => {
        console.log('WebSocket connecté');
        setSessionData(prev => ({ ...prev, isConnected: true, error: null }));
        reconnectAttemptsRef.current = 0;

        // Rejoindre la session
        socket.emit('join-session', { sessionCode, userId, isHost });

        // Démarrer le heartbeat
        startHeartbeat();
      });

      socket.on('disconnect', (reason) => {
        console.log('WebSocket déconnecté:', reason);
        setSessionData(prev => ({ ...prev, isConnected: false }));
        stopHeartbeat();

        // Tentative de reconnexion automatique
        if (reason === 'io server disconnect') {
          // Le serveur a fermé la connexion, ne pas reconnecter automatiquement
          return;
        }

        if (reconnectAttemptsRef.current < WEBSOCKET_CONFIG.reconnectAttempts) {
          setTimeout(() => {
            reconnectAttemptsRef.current++;
            connect();
          }, WEBSOCKET_CONFIG.reconnectDelay * reconnectAttemptsRef.current);
        }
      });

      socket.on('connect_error', (error) => {
        console.error('Erreur de connexion WebSocket:', error);
        setSessionData(prev => ({ 
          ...prev, 
          isConnected: false, 
          error: 'Erreur de connexion au serveur' 
        }));
      });

      // Événements de session
      socket.on('session-joined', (data: { playlist: Song[]; users: User[]; sessionState: SessionState }) => {
        setSessionData(prev => ({
          ...prev,
          playlist: data.playlist,
          users: data.users,
          sessionState: data.sessionState,
          currentSong: data.playlist.find(song => song.isPlaying) || null,
        }));
      });

      socket.on('playlist-updated', (data: { playlist: Song[] }) => {
        setSessionData(prev => ({
          ...prev,
          playlist: data.playlist,
          currentSong: data.playlist.find(song => song.isPlaying) || null,
        }));
      });

      socket.on('user-joined', (data: { user: User }) => {
        setSessionData(prev => ({
          ...prev,
          users: [...prev.users.filter(u => u.id !== data.user.id), data.user],
        }));
      });

      socket.on('user-left', (data: { userId: string }) => {
        setSessionData(prev => ({
          ...prev,
          users: prev.users.filter(u => u.id !== data.userId),
        }));
      });

      socket.on('song-voted', (data: { songId: string; votes: { [emoji: string]: number } }) => {
        setSessionData(prev => ({
          ...prev,
          playlist: prev.playlist.map(song =>
            song.id === data.songId ? { ...song, votes: data.votes } : song
          ),
        }));
      });

      socket.on('playback-state', (data: SessionState) => {
        setSessionData(prev => ({
          ...prev,
          sessionState: data,
        }));
      });

      socket.on('error', (data: { message: string }) => {
        setSessionData(prev => ({ ...prev, error: data.message }));
      });

    } catch (error) {
      console.error('Erreur lors de la création de la connexion WebSocket:', error);
      setSessionData(prev => ({ 
        ...prev, 
        error: 'Impossible de se connecter au serveur' 
      }));
    }
  }, [sessionCode, userId, isHost]);

  // Déconnexion
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      stopHeartbeat();
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    setSessionData(prev => ({ ...prev, isConnected: false }));
  }, []);

  // Heartbeat pour maintenir la connexion
  const startHeartbeat = useCallback(() => {
    heartbeatIntervalRef.current = setInterval(() => {
      if (socketRef.current?.connected) {
        socketRef.current.emit('ping');
      }
    }, WEBSOCKET_CONFIG.heartbeatInterval);
  }, []);

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  // Actions de session
  const addSong = useCallback((song: Omit<Song, 'id' | 'votes' | 'submittedBy'>) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('add-song', { ...song, sessionCode, userId });
    }
  }, [sessionCode, userId]);

  const voteSong = useCallback((songId: string, emoji: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('vote-song', { songId, emoji, userId });
    }
  }, [userId]);

  const removeSong = useCallback((songId: string) => {
    if (socketRef.current?.connected && isHost) {
      socketRef.current.emit('remove-song', { songId, sessionCode });
    }
  }, [sessionCode, isHost]);

  // Contrôles de lecture (hôte uniquement)
  const playPause = useCallback(() => {
    if (socketRef.current?.connected && isHost) {
      socketRef.current.emit('playback-control', { 
        action: 'play-pause', 
        sessionCode 
      });
    }
  }, [sessionCode, isHost]);

  const nextSong = useCallback(() => {
    if (socketRef.current?.connected && isHost) {
      socketRef.current.emit('playback-control', { 
        action: 'next', 
        sessionCode 
      });
    }
  }, [sessionCode, isHost]);

  const previousSong = useCallback(() => {
    if (socketRef.current?.connected && isHost) {
      socketRef.current.emit('playback-control', { 
        action: 'previous', 
        sessionCode 
      });
    }
  }, [sessionCode, isHost]);

  const setVolume = useCallback((volume: number) => {
    if (socketRef.current?.connected && isHost) {
      socketRef.current.emit('playback-control', { 
        action: 'volume', 
        value: volume,
        sessionCode 
      });
    }
  }, [sessionCode, isHost]);

  const seekTo = useCallback((position: number) => {
    if (socketRef.current?.connected && isHost) {
      socketRef.current.emit('playback-control', { 
        action: 'seek', 
        value: position,
        sessionCode 
      });
    }
  }, [sessionCode, isHost]);

  // Effets
  useEffect(() => {
    connect();
    return () => disconnect();
  }, [connect, disconnect]);

  // Nettoyage lors du démontage
  useEffect(() => {
    return () => {
      stopHeartbeat();
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [stopHeartbeat]);

  return {
    // Données
    ...sessionData,
    
    // Actions
    addSong,
    voteSong,
    removeSong,
    
    // Contrôles de lecture (hôte)
    playPause,
    nextSong,
    previousSong,
    setVolume,
    seekTo,
    
    // Connexion
    connect,
    disconnect,
    
    // État
    isHost,
  };
}
