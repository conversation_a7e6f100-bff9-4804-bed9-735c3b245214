'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { SpotifyTrack, SearchResult } from '@/types';
import { SPOTIFY_CONFIG, MUSIC_CONFIG } from '@/config/constants';

interface SpotifyPlayer {
  connect: () => Promise<boolean>;
  disconnect: () => void;
  getCurrentState: () => Promise<any>;
  getVolume: () => Promise<number>;
  nextTrack: () => Promise<void>;
  pause: () => Promise<void>;
  previousTrack: () => Promise<void>;
  resume: () => Promise<void>;
  seek: (position: number) => Promise<void>;
  setVolume: (volume: number) => Promise<void>;
  togglePlay: () => Promise<void>;
}

interface UseSpotifyReturn {
  // État
  isConnected: boolean;
  isReady: boolean;
  accessToken: string | null;
  error: string | null;
  
  // Recherche
  searchTracks: (query: string) => Promise<SearchResult[]>;
  isSearching: boolean;
  
  // Lecture (pour l'hôte)
  player: SpotifyPlayer | null;
  deviceId: string | null;
  
  // Authentification
  login: () => void;
  logout: () => void;
  
  // Contrôles
  play: (trackUri?: string) => Promise<void>;
  pause: () => Promise<void>;
  next: () => Promise<void>;
  previous: () => Promise<void>;
  seek: (position: number) => Promise<void>;
  setVolume: (volume: number) => Promise<void>;
}

export function useSpotify(): UseSpotifyReturn {
  const [isConnected, setIsConnected] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [player, setPlayer] = useState<SpotifyPlayer | null>(null);
  const [deviceId, setDeviceId] = useState<string | null>(null);
  
  const playerRef = useRef<any>(null);

  // Initialisation du SDK Spotify
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Charger le SDK Spotify
    const script = document.createElement('script');
    script.src = 'https://sdk.scdn.co/spotify-player.js';
    script.async = true;
    document.body.appendChild(script);

    window.onSpotifyWebPlaybackSDKReady = () => {
      setIsReady(true);
    };

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  // Récupération du token d'accès depuis l'URL ou le localStorage
  useEffect(() => {
    const token = getAccessTokenFromUrl() || localStorage.getItem('spotify_access_token');
    if (token) {
      setAccessToken(token);
      localStorage.setItem('spotify_access_token', token);
      // Nettoyer l'URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  // Initialisation du player quand le SDK est prêt et qu'on a un token
  useEffect(() => {
    if (!isReady || !accessToken || playerRef.current) return;

    const spotifyPlayer = new (window as any).Spotify.Player({
      name: 'PlayHify Web Player',
      getOAuthToken: (cb: (token: string) => void) => {
        cb(accessToken);
      },
      volume: MUSIC_CONFIG.defaultVolume / 100,
    });

    // Événements du player
    spotifyPlayer.addListener('ready', ({ device_id }: { device_id: string }) => {
      console.log('Spotify Player prêt avec Device ID:', device_id);
      setDeviceId(device_id);
      setIsConnected(true);
      setError(null);
    });

    spotifyPlayer.addListener('not_ready', ({ device_id }: { device_id: string }) => {
      console.log('Spotify Player non prêt avec Device ID:', device_id);
      setIsConnected(false);
    });

    spotifyPlayer.addListener('initialization_error', ({ message }: { message: string }) => {
      console.error('Erreur d\'initialisation Spotify:', message);
      setError(message);
    });

    spotifyPlayer.addListener('authentication_error', ({ message }: { message: string }) => {
      console.error('Erreur d\'authentification Spotify:', message);
      setError(message);
      setAccessToken(null);
      localStorage.removeItem('spotify_access_token');
    });

    spotifyPlayer.addListener('account_error', ({ message }: { message: string }) => {
      console.error('Erreur de compte Spotify:', message);
      setError(message);
    });

    spotifyPlayer.addListener('playback_error', ({ message }: { message: string }) => {
      console.error('Erreur de lecture Spotify:', message);
      setError(message);
    });

    // Connecter le player
    spotifyPlayer.connect();
    
    playerRef.current = spotifyPlayer;
    setPlayer(spotifyPlayer);

    return () => {
      if (playerRef.current) {
        playerRef.current.disconnect();
      }
    };
  }, [isReady, accessToken]);

  // Fonction pour extraire le token d'accès de l'URL
  const getAccessTokenFromUrl = (): string | null => {
    if (typeof window === 'undefined') return null;
    
    const hash = window.location.hash;
    if (!hash) return null;

    const params = new URLSearchParams(hash.substring(1));
    return params.get('access_token');
  };

  // Authentification
  const login = useCallback(() => {
    const params = new URLSearchParams({
      client_id: SPOTIFY_CONFIG.clientId,
      response_type: 'token',
      redirect_uri: SPOTIFY_CONFIG.redirectUri,
      scope: SPOTIFY_CONFIG.scopes.join(' '),
      show_dialog: 'true',
    });

    window.location.href = `https://accounts.spotify.com/authorize?${params.toString()}`;
  }, []);

  const logout = useCallback(() => {
    setAccessToken(null);
    setIsConnected(false);
    localStorage.removeItem('spotify_access_token');
    
    if (playerRef.current) {
      playerRef.current.disconnect();
      playerRef.current = null;
      setPlayer(null);
    }
  }, []);

  // Recherche de musiques
  const searchTracks = useCallback(async (query: string): Promise<SearchResult[]> => {
    if (!accessToken || !query.trim()) return [];

    setIsSearching(true);
    setError(null);

    try {
      const response = await fetch(
        `${SPOTIFY_CONFIG.apiBaseUrl}/search?q=${encodeURIComponent(query)}&type=track&limit=${MUSIC_CONFIG.maxSearchResults}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Erreur API Spotify: ${response.status}`);
      }

      const data = await response.json();
      
      const results: SearchResult[] = data.tracks.items.map((track: SpotifyTrack) => ({
        id: track.id,
        title: track.name,
        artist: track.artists.map(artist => artist.name).join(', '),
        album: track.album.name,
        duration: Math.floor(track.duration_ms / 1000),
        albumCover: track.album.images[0]?.url,
        spotifyId: track.id,
      }));

      return results;
    } catch (error) {
      console.error('Erreur lors de la recherche Spotify:', error);
      setError('Erreur lors de la recherche');
      return [];
    } finally {
      setIsSearching(false);
    }
  }, [accessToken]);

  // Contrôles de lecture
  const play = useCallback(async (trackUri?: string) => {
    if (!accessToken || !deviceId) return;

    try {
      const body = trackUri ? { uris: [trackUri] } : undefined;
      
      await fetch(`${SPOTIFY_CONFIG.apiBaseUrl}/me/player/play?device_id=${deviceId}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });
    } catch (error) {
      console.error('Erreur lors de la lecture:', error);
      setError('Erreur lors de la lecture');
    }
  }, [accessToken, deviceId]);

  const pause = useCallback(async () => {
    if (!player) return;
    try {
      await player.pause();
    } catch (error) {
      console.error('Erreur lors de la pause:', error);
      setError('Erreur lors de la pause');
    }
  }, [player]);

  const next = useCallback(async () => {
    if (!player) return;
    try {
      await player.nextTrack();
    } catch (error) {
      console.error('Erreur lors du passage au suivant:', error);
      setError('Erreur lors du passage au suivant');
    }
  }, [player]);

  const previous = useCallback(async () => {
    if (!player) return;
    try {
      await player.previousTrack();
    } catch (error) {
      console.error('Erreur lors du passage au précédent:', error);
      setError('Erreur lors du passage au précédent');
    }
  }, [player]);

  const seek = useCallback(async (position: number) => {
    if (!player) return;
    try {
      await player.seek(position * 1000); // Spotify attend des millisecondes
    } catch (error) {
      console.error('Erreur lors du seek:', error);
      setError('Erreur lors du seek');
    }
  }, [player]);

  const setVolume = useCallback(async (volume: number) => {
    if (!player) return;
    try {
      await player.setVolume(volume / 100); // Spotify attend une valeur entre 0 et 1
    } catch (error) {
      console.error('Erreur lors du changement de volume:', error);
      setError('Erreur lors du changement de volume');
    }
  }, [player]);

  return {
    // État
    isConnected,
    isReady,
    accessToken,
    error,
    
    // Recherche
    searchTracks,
    isSearching,
    
    // Lecture
    player,
    deviceId,
    
    // Authentification
    login,
    logout,
    
    // Contrôles
    play,
    pause,
    next,
    previous,
    seek,
    setVolume,
  };
}

// Déclaration globale pour TypeScript
declare global {
  interface Window {
    onSpotifyWebPlaybackSDKReady: () => void;
    Spotify: any;
  }
}
