// Types pour l'application PlayHify

export interface Song {
  id: string;
  title: string;
  artist: string;
  album: string;
  albumCover?: string;
  duration: number; // en secondes
  submittedBy: string;
  votes: { [emoji: string]: number };
  isPlaying?: boolean;
  position?: number; // position dans la playlist
  spotifyId?: string;
  createdAt?: Date;
}

export interface User {
  id: string;
  nickname: string;
  points: number;
  isHost: boolean;
  isOnline: boolean;
  joinedAt: Date;
  spotifyId?: string; // pour l'hôte uniquement
}

export interface Session {
  id: string;
  code: string; // code de 6 caractères
  name: string;
  hostId: string;
  isActive: boolean;
  createdAt: Date;
  settings: SessionSettings;
}

export interface SessionSettings {
  allowVoting: boolean;
  maxSongsPerUser: number; // -1 pour illimité
  votingWeight: number;
  requireApproval?: boolean; // l'hôte doit approuver les ajouts
  allowDuplicates?: boolean;
}

export interface Vote {
  id: string;
  songId: string;
  userId: string;
  emoji: string; // '👍', '👎', '🔥', '❤️', etc.
  value: number; // poids du vote
  createdAt: Date;
}

export interface SessionState {
  sessionId: string;
  currentSongId?: string;
  isPlaying: boolean;
  currentPosition: number; // position actuelle en secondes
  volume: number; // 0-100
  updatedAt: Date;
}

export interface SpotifyTrack {
  id: string;
  name: string;
  artists: Array<{ name: string }>;
  album: {
    name: string;
    images: Array<{ url: string; height: number; width: number }>;
  };
  duration_ms: number;
  external_urls: {
    spotify: string;
  };
}

export interface SearchResult {
  id: string;
  title: string;
  artist: string;
  album: string;
  duration: number;
  albumCover?: string;
  spotifyId: string;
}

// Types pour les événements WebSocket
export interface WebSocketEvent {
  type: string;
  payload: any;
  timestamp: Date;
}

export interface PlaylistUpdatedEvent extends WebSocketEvent {
  type: 'playlist-updated';
  payload: {
    playlist: Song[];
    sessionId: string;
  };
}

export interface SongVotedEvent extends WebSocketEvent {
  type: 'song-voted';
  payload: {
    songId: string;
    userId: string;
    emoji: string;
    newVoteCount: number;
  };
}

export interface PlaybackStateEvent extends WebSocketEvent {
  type: 'playback-state';
  payload: SessionState;
}

export interface UserJoinedEvent extends WebSocketEvent {
  type: 'user-joined';
  payload: {
    user: User;
    sessionId: string;
  };
}

export interface UserLeftEvent extends WebSocketEvent {
  type: 'user-left';
  payload: {
    userId: string;
    sessionId: string;
  };
}

export interface NotificationEvent extends WebSocketEvent {
  type: 'notification';
  payload: {
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    userId?: string; // si null, notification pour tous
  };
}

// Types pour les API endpoints
export interface CreateSessionRequest {
  name: string;
  settings: SessionSettings;
  hostSpotifyId?: string;
}

export interface CreateSessionResponse {
  session: Session;
  code: string;
}

export interface JoinSessionRequest {
  code: string;
  nickname: string;
}

export interface JoinSessionResponse {
  session: Session;
  user: User;
  playlist: Song[];
  users: User[];
}

export interface AddSongRequest {
  sessionId: string;
  spotifyId: string;
  userId: string;
}

export interface VoteSongRequest {
  songId: string;
  userId: string;
  emoji: string;
}

export interface PlaybackControlRequest {
  sessionId: string;
  action: 'play' | 'pause' | 'next' | 'previous' | 'seek';
  value?: number; // pour seek (position en secondes) ou volume
}

// Types pour les erreurs
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// Types pour les statistiques
export interface SessionStats {
  totalSongs: number;
  totalVotes: number;
  totalUsers: number;
  activeUsers: number;
  totalDuration: number; // en secondes
  topContributor: string; // nickname
  mostVotedSong: Song;
}

// Types pour la gamification
export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  condition: string; // description de la condition pour obtenir le badge
}

export interface UserBadge {
  userId: string;
  badgeId: string;
  earnedAt: Date;
}

export interface Leaderboard {
  sessionId: string;
  users: Array<{
    user: User;
    rank: number;
    badges: Badge[];
  }>;
  updatedAt: Date;
}

// Types pour les préférences utilisateur
export interface UserPreferences {
  userId: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: boolean;
  autoJoinVoice: boolean;
  preferredGenres: string[];
}

// Utilitaires de type
export type VoteEmoji = '👍' | '👎' | '🔥' | '❤️' | '😍' | '🎵' | '💯';

export type SessionRole = 'host' | 'participant';

export type PlaybackAction = 'play' | 'pause' | 'next' | 'previous' | 'seek' | 'volume';

export type NotificationType = 'info' | 'success' | 'warning' | 'error';
